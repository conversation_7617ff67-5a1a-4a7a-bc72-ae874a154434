[{"algorithm": "AES-256对称密钥", "success": true, "processing_time_ms": 0.5857944488525391, "key_id": "demo-aes-256-key-001", "key_type": "symmetric", "key_size": 256, "fingerprint": "76:66:cf:6a:ec:dc:55:ee:09:f3:5b:c3:44:d7:14:4e:64:4a:6c:37:f7:6a:47:d5:ec:16:85:e8:14:5e:da:47", "has_symmetric_key": true, "created_at": "2025-06-23 17:04:57.104203", "stored_in_vault": false, "export_success": true, "exported_key_length": 44}, {"algorithm": "RSA-2048密钥对", "success": true, "processing_time_ms": 60.88662147521973, "key_id": "demo-rsa-2048-keypair-001", "key_type": "asymmetric_private", "key_size": 2048, "fingerprint": "2b:18:5c:24:a9:0d:bc:9c:d6:8e:c8:c3:15:00:3c:be:43:bb:70:86:dc:5e:f5:11:50:c5:92:b5:4a:62:38:93", "has_public_key": true, "has_private_key": true, "public_key_length": 451, "private_key_length": 1708, "created_at": "2025-06-23 17:04:57.166356", "stored_in_vault": false, "export_success": true, "exported_public_key_length": 451}, {"algorithm": "ECDSA P-256密钥对", "success": true, "processing_time_ms": 0.9622573852539062, "key_id": "demo-ecdsa-p256-keypair-001", "key_type": "asymmetric_private", "key_size": 256, "fingerprint": "17:6a:0a:87:bf:ec:28:fb:0c:0d:dd:2f:bb:3d:d8:f9:17:69:74:1e:9e:80:c7:b4:df:f3:c0:ca:7f:1a:53:bc", "has_public_key": true, "has_private_key": true, "public_key_length": 178, "private_key_length": 241, "created_at": "2025-06-23 17:04:57.169838", "stored_in_vault": false}, {"algorithm": "SM2密钥对（国密）", "success": true, "processing_time_ms": 0.5688667297363281, "mode_info": "运行模式: dev", "key_id": "demo-sm2-keypair-001", "key_type": "asymmetric_private", "key_size": 256, "fingerprint": "fa:70:d1:a2:00:30:0a:f0:21:8a:50:a7:c5:08:a5:9f:fa:ae:d8:c6:8a:69:47:10:fa:de:f1:b2:f1:de:73:b5", "has_public_key": true, "has_private_key": true, "public_key_length": 178, "private_key_length": 241, "created_at": "2025-06-23 17:04:57.171392", "stored_in_vault": false}]