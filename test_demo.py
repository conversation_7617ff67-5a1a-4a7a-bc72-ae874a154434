#!/usr/bin/env python3
"""
测试演示程序
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_encryption_demo():
    """测试加密演示"""
    try:
        from demos.crypto.encryption_demo import EncryptionDemo
        
        print("🔐 测试加密演示...")
        demo = EncryptionDemo(mode="dev")
        await demo.initialize()
        
        # 测试AES-GCM加密
        result = await demo.demo_aes_gcm_encryption()
        print(f"AES-GCM测试结果: {result['success']}")
        
        await demo.cleanup()
        print("✅ 加密演示测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 加密演示测试失败: {e}")
        return False

async def test_signature_demo():
    """测试签名演示"""
    try:
        from demos.crypto.signature_demo import SignatureDemo
        
        print("✍️ 测试签名演示...")
        demo = SignatureDemo(mode="dev")
        await demo.initialize()
        
        # 测试SM2签名
        result = await demo.demo_sm2_signature()
        print(f"SM2签名测试结果: {result['success']}")
        
        await demo.cleanup()
        print("✅ 签名演示测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 签名演示测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试演示程序")
    print("=" * 50)
    
    results = []
    
    # 测试加密演示
    results.append(await test_encryption_demo())
    
    # 测试签名演示
    results.append(await test_signature_demo())
    
    # 显示结果
    print("\n📊 测试结果:")
    print(f"成功: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")

if __name__ == "__main__":
    asyncio.run(main())
