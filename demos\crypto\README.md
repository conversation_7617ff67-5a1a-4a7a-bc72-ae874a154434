# TestGenius 加密算法演示程序

本目录包含TestGenius项目加密模块的完整演示程序，展示了各种加密算法的使用方法和功能特性。

## 📁 文件结构

```
demos/crypto/
├── README.md                 # 本说明文件
├── main_demo.py             # 主演示程序（推荐使用）
├── encryption_demo.py       # 加密功能演示
├── signature_demo.py        # 数字签名演示
├── decryption_demo.py       # 解密功能演示
├── verification_demo.py     # 验签功能演示
└── performance_demo.py      # 性能测试和安全验证
```

## 🚀 快速开始

### 运行完整演示

```bash
# 进入项目根目录
cd /path/to/TestGenius

# 运行完整的加密算法演示
python demos/crypto/main_demo.py
```

### 运行单独模块演示

```bash
# 加密演示
python demos/crypto/encryption_demo.py

# 数字签名演示
python demos/crypto/signature_demo.py

# 解密演示
python demos/crypto/decryption_demo.py

# 验签演示
python demos/crypto/verification_demo.py

# 性能测试演示
python demos/crypto/performance_demo.py
```

## 🔐 功能特性

### 1. 加密演示 (encryption_demo.py)
- **AES-GCM加密**: 展示高安全性的认证加密
- **SM4国密加密**: 中国国家密码标准算法
- **文件加密**: 演示大文件加密处理
- **多种数据格式**: 支持文本、二进制数据加密

### 2. 数字签名演示 (signature_demo.py)
- **SM2国密签名**: 椭圆曲线数字签名算法
- **RSA-PSS签名**: 概率签名方案
- **JSON数据签名**: 结构化数据签名
- **批量签名**: 高效的批量数据签名处理

### 3. 解密演示 (decryption_demo.py)
- **往返验证**: 加密-解密完整性验证
- **错误处理**: 各种解密失败场景处理
- **数据完整性**: 验证解密数据的正确性

### 4. 验签演示 (verification_demo.py)
- **签名验证**: 数字签名有效性验证
- **失败场景**: 篡改检测和错误处理
- **往返测试**: 签名-验签完整流程

### 5. 性能测试 (performance_demo.py)
- **基准测试**: 各算法性能对比
- **压力测试**: 大数据量处理能力
- **安全验证**: 密钥隔离和数据完整性
- **并发测试**: 多线程处理性能

## 🛠️ 支持的算法

### 加密算法
- **AES-GCM**: 256位密钥，认证加密
- **AES-CBC**: 传统分组密码模式
- **SM4**: 中国国密对称加密算法
- **3DES**: 三重数据加密标准

### 签名算法
- **SM2**: 中国国密椭圆曲线数字签名
- **RSA-PSS**: RSA概率签名方案
- **RSA-PKCS1**: RSA传统签名方案
- **ECDSA**: 椭圆曲线数字签名算法

### 哈希算法
- **SM3**: 中国国密哈希算法
- **SHA256**: 安全哈希算法256位
- **SHA1**: 安全哈希算法160位
- **MD5**: 消息摘要算法（仅用于校验）

## 📊 演示输出

### 控制台输出特性
- 🔐 **清晰的操作标识**: 使用emoji和颜色区分不同操作
- ⏱️ **详细的时间统计**: 每个操作的处理时间
- 📈 **性能指标**: 吞吐量、成功率等关键指标
- ✅ **状态反馈**: 明确的成功/失败状态显示
- 💡 **提示信息**: 开发模式下的额外说明

### 结果文件
- **JSON格式**: 详细的测试结果和统计数据
- **时间戳**: 每次运行的时间记录
- **性能数据**: 可用于性能分析和优化

## 🔧 运行模式

### 开发模式 (dev)
- **模拟实现**: 使用模拟的密钥和算法
- **快速响应**: 适合开发和测试环境
- **详细日志**: 提供调试信息
- **无外部依赖**: 不需要Vault等外部服务

### 生产模式 (production)
- **真实密钥**: 集成Vault密钥管理系统
- **严格验证**: 完整的安全检查和审计
- **性能优化**: 针对生产环境优化
- **错误处理**: 完善的异常处理机制

## 📋 系统要求

### Python版本
- Python 3.8+

### 依赖库
- `asyncio`: 异步编程支持
- `cryptography`: 加密算法实现
- `pydantic`: 数据模型验证
- `hvac`: Vault客户端（生产模式）
- `gmssl`: 国密算法库（可选）

### 安装依赖
```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装国密算法库（可选）
pip install gmssl
```

## 🔍 使用示例

### 基本加密示例
```python
from demos.crypto.encryption_demo import EncryptionDemo

# 创建演示实例
demo = EncryptionDemo(mode="dev")

# 运行演示
results = await demo.run_all_demos()
```

### 性能测试示例
```python
from demos.crypto.performance_demo import PerformanceDemo

# 创建性能测试实例
perf_demo = PerformanceDemo(mode="dev")

# 运行性能测试
results = await perf_demo.run_all_performance_tests()
```

## 🐛 故障排除

### 常见问题

1. **ImportError: No module named 'gmssl'**
   - 解决方案: 安装gmssl库或在开发模式下运行
   - 命令: `pip install gmssl`

2. **Vault连接失败**
   - 解决方案: 检查Vault配置或使用开发模式
   - 在开发模式下会使用模拟实现

3. **权限错误**
   - 解决方案: 确保有足够的文件系统权限
   - 检查临时文件目录的写入权限

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=/path/to/TestGenius
python -v demos/crypto/main_demo.py
```

## 📈 性能优化建议

1. **大数据处理**: 使用流式处理避免内存溢出
2. **并发处理**: 利用异步编程提高吞吐量
3. **密钥缓存**: 合理使用密钥缓存减少Vault调用
4. **算法选择**: 根据安全需求选择合适的算法

## 🔒 安全注意事项

1. **密钥管理**: 生产环境必须使用Vault等安全的密钥管理系统
2. **日志安全**: 避免在日志中记录敏感信息
3. **网络安全**: 生产环境使用mTLS保护通信
4. **权限控制**: 实施最小权限原则

## 📞 技术支持

如有问题或建议，请联系TestGenius开发团队：
- 项目仓库: [TestGenius GitHub](https://github.com/your-org/TestGenius)
- 文档: 查看项目docs目录
- 问题反馈: 通过GitHub Issues提交

---

**TestGenius 加密模块演示程序** - 展示企业级加密功能的完整实现
