"""
测试CryptoClient的真实加密/解密功能
"""

import pytest
import asyncio
from uuid import uuid4
import os
import base64
from unittest.mock import MagicMock
from pytest_mock import MockerFixture

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    EncryptionConfig,
    KeyConfig,
    KeyType,
    EncryptionAlgorithm,
    VaultConfig,
)

@pytest.fixture(scope="module")
async def crypto_client() -> CryptoClient:
    """提供一个CryptoClient实例"""
    client = CryptoClient()
    await client.initialize()
    yield client
    await client.cleanup()

@pytest.fixture
def mock_vault_client(mocker: MockerFixture) -> MagicMock:
    """模拟hvac.Client"""
    # 模拟Vault客户端实例
    mock_client = MagicMock()
    mock_client.is_authenticated.return_value = True
    
    # 模拟密钥数据
    secret_key = os.urandom(32)
    secret_key_b64 = base64.b64encode(secret_key).decode('utf-8')

    # 模拟 read_secret_version 的返回值
    mock_client.secrets.kv.v2.read_secret_version.return_value = {
        'data': {
            'data': {
                'key': secret_key_b64
            }
        }
    }
    
    # 使用mocker替换hvac.Client的构造函数
    mocker.patch('hvac.Client', return_value=mock_client)
    
    return mock_client

@pytest.mark.asyncio
async def test_encrypt_decrypt_with_mock_vault(mock_vault_client: MagicMock):
    """
    测试使用模拟的Vault客户端进行加密和解密
    """
    # 1. 初始化CryptoClient，它将使用被mock的hvac.Client
    vault_config = VaultConfig(vault_url="http://mock-vault:8200", vault_token="mock_token")
    client = CryptoClient(vault_config=vault_config)
    await client.initialize()

    # 2. 准备加密请求
    original_text = "Message encrypted via Vault key"
    key_config = KeyConfig(
        key_id="vault-test-key",
        key_type=KeyType.SYMMETRIC,
        algorithm=EncryptionAlgorithm.AES_GCM,
        vault_path="secret/data/keys/vault-test-key"
    )
    encryption_config = EncryptionConfig(
        algorithm=EncryptionAlgorithm.AES_GCM,
        key_config=key_config
    )
    
    encrypt_request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
        data=original_text,
        encryption_config=encryption_config,
        key_generation_config=None,
        key_export_config=None,
)

    # 3. 处理加密请求
    encrypt_response = await client.process_request(encrypt_request)
    assert encrypt_response.success is True
    encrypted_data = encrypt_response.result

    # 验证 hvac client 是否被调用
    mock_vault_client.secrets.kv.v2.read_secret_version.assert_called_once_with(
        path=key_config.vault_path
    )

    # 4. 处理解密请求
    decrypt_request = CryptoRequest(
operation=CryptoOperation.DECRYPT,
        data=encrypted_data,
        encryption_config=encryption_config,
        key_generation_config=None,
        key_export_config=None,
)
    decrypt_response = await client.process_request(decrypt_request)
    assert decrypt_response.success is True
    assert decrypt_response.result == original_text

    await client.cleanup()

@pytest.mark.asyncio
async def test_encrypt_decrypt_aes_gcm(crypto_client: CryptoClient):
    """
    测试使用AES-GCM进行加密和解密的完整流程
    """
    # 1. 准备测试数据和配置
    original_text = "This is a secret message for TestGenius!"
    key_id = "test-key-aes-256-gcm"
    
    key_config = KeyConfig(
        key_id=key_id,
        key_type=KeyType.SYMMETRIC,
        algorithm=EncryptionAlgorithm.AES_GCM,
        key_size=256
    )
    
    encryption_config = EncryptionConfig(
        algorithm=EncryptionAlgorithm.AES_GCM,
        key_config=key_config
    )

    # 2. 创建并处理加密请求
    encrypt_request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
        data=original_text,
        encryption_config=encryption_config,
        key_generation_config=None,
        key_export_config=None,
)
    
    encrypt_response = await crypto_client.process_request(encrypt_request)
    
    # 验证加密响应
    assert encrypt_response.success is True
    assert encrypt_response.error_message is None
    assert isinstance(encrypt_response.result, str)
    assert encrypt_response.result != original_text
    
    encrypted_data = encrypt_response.result

    # 3. 创建并处理解密请求
    decrypt_request = CryptoRequest(
operation=CryptoOperation.DECRYPT,
        data=encrypted_data,
        encryption_config=encryption_config,
        key_generation_config=None,
        key_export_config=None,
)
    
    decrypt_response = await crypto_client.process_request(decrypt_request)
    
    # 验证解密响应
    assert decrypt_response.success is True
    assert decrypt_response.error_message is None
    assert decrypt_response.result == original_text
    
    print(f"\nOriginal Text: '{original_text}'")
    print(f"Encrypted (Base64): '{encrypted_data}'")
    print(f"Decrypted Text: '{decrypt_response.result}'")
    
@pytest.mark.asyncio
async def test_decrypt_with_wrong_key(crypto_client: CryptoClient):
    """
    测试使用错误的密钥解密时应失败
    """
    # 1. 加密
    original_text = "Another secret message."
    correct_key_id = "correct-key-aes-256"
    
    correct_key_config = KeyConfig(
        key_id=correct_key_id, key_type=KeyType.SYMMETRIC, algorithm=EncryptionAlgorithm.AES_GCM
    )
    correct_encryption_config = EncryptionConfig(
        algorithm=EncryptionAlgorithm.AES_GCM, key_config=correct_key_config
    )
    
    encrypt_request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
        data=original_text,
        encryption_config=correct_encryption_config,
        key_generation_config=None,
        key_export_config=None,
)
    encrypt_response = await crypto_client.process_request(encrypt_request)
    encrypted_data = encrypt_response.result

    # 2. 使用错误的密钥配置尝试解密
    wrong_key_id = "wrong-key-aes-256"
    wrong_key_config = KeyConfig(
        key_id=wrong_key_id, key_type=KeyType.SYMMETRIC, algorithm=EncryptionAlgorithm.AES_GCM
    )
    wrong_encryption_config = EncryptionConfig(
        algorithm=EncryptionAlgorithm.AES_GCM, key_config=wrong_key_config
    )
    
    decrypt_request = CryptoRequest(
operation=CryptoOperation.DECRYPT,
        data=encrypted_data,
        encryption_config=wrong_encryption_config,
        key_generation_config=None,
        key_export_config=None,
)
    
    decrypt_response = await crypto_client.process_request(decrypt_request)
    
    # 验证解密失败
    assert decrypt_response.success is False
    assert decrypt_response.result is None
    assert "Decryption failed" in decrypt_response.error_message 