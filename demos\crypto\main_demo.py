"""
TestGenius 加密算法主演示程序

本程序整合了所有加密功能模块的演示，包括：
- 加密演示 (AES-GCM, SM4等)
- 数字签名演示 (SM2, RSA-PSS等)
- 解密演示 (往返验证)
- 验签演示 (签名验证)
- 性能测试和安全验证
- 开发模式和生产模式对比

提供完整的控制台输出，包含操作耗时统计和流程展示。
"""

import asyncio
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入各个演示模块
from demos.crypto.encryption_demo import EncryptionDemo
from demos.crypto.signature_demo import SignatureDemo
from demos.crypto.decryption_demo import DecryptionDemo
from demos.crypto.verification_demo import VerificationDemo
from demos.crypto.performance_demo import PerformanceDemo


class CryptoMainDemo:
    """加密算法主演示类"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.all_results = []
        self.performance_stats = {}
        
    def print_header(self):
        """打印程序头部信息"""
        print("=" * 80)
        print("🔐 TestGenius 加密算法完整演示程序")
        print("=" * 80)
        print("本演示程序展示TestGenius项目中加密模块的完整功能：")
        print("• 🔐 加密演示 - SM2、SM3、SM4等国密算法及AES等标准算法")
        print("• ✍️  数字签名演示 - 完整的签名生成流程")
        print("• 🔓 解密演示 - 加密解密往返验证")
        print("• 🔍 验签演示 - 数字签名验证流程")
        print("• 📊 性能测试 - 各算法性能对比")
        print("• ⚙️  模式对比 - 开发模式vs生产模式")
        print("=" * 80)
        print()
        
    def print_section_header(self, title: str, icon: str = "🔹"):
        """打印章节标题"""
        print(f"\n{icon} {title}")
        print("─" * (len(title) + 4))
        
    async def run_encryption_demos(self, mode: str = "dev") -> List[Dict[str, Any]]:
        """运行加密演示"""
        self.print_section_header("加密功能演示", "🔐")
        
        demo = EncryptionDemo(mode=mode)
        results = await demo.run_all_demos()
        
        # 收集性能统计
        for result in results:
            if result["success"]:
                algo_name = result["algorithm"]
                if algo_name not in self.performance_stats:
                    self.performance_stats[algo_name] = {"encrypt": [], "decrypt": []}
                self.performance_stats[algo_name]["encrypt"].append(result["processing_time_ms"])
        
        return results
        
    async def run_signature_demos(self, mode: str = "dev") -> List[Dict[str, Any]]:
        """运行数字签名演示"""
        self.print_section_header("数字签名功能演示", "✍️")
        
        demo = SignatureDemo(mode=mode)
        results = await demo.run_all_demos()
        
        # 收集性能统计
        for result in results:
            if result["success"]:
                algo_name = result["algorithm"]
                if algo_name not in self.performance_stats:
                    self.performance_stats[algo_name] = {"sign": [], "verify": []}
                
                if "processing_time_ms" in result:
                    self.performance_stats[algo_name]["sign"].append(result["processing_time_ms"])
                elif "total_processing_time_ms" in result:  # 批量签名
                    avg_time = result["average_processing_time_ms"]
                    self.performance_stats[algo_name]["sign"].append(avg_time)
        
        return results
        
    async def run_decryption_demos(self, mode: str = "dev") -> List[Dict[str, Any]]:
        """运行解密演示"""
        self.print_section_header("解密功能演示", "🔓")
        
        demo = DecryptionDemo(mode=mode)
        results = await demo.run_all_demos()
        
        # 收集性能统计
        for result in results:
            if result["success"] and "decrypt_time_ms" in result:
                algo_name = result["algorithm"].split(" ")[0]  # 提取算法名称
                if algo_name not in self.performance_stats:
                    self.performance_stats[algo_name] = {"encrypt": [], "decrypt": []}
                self.performance_stats[algo_name]["decrypt"].append(result["decrypt_time_ms"])
        
        return results
        
    async def run_verification_demos(self, mode: str = "dev") -> List[Dict[str, Any]]:
        """运行验签演示"""
        self.print_section_header("验签功能演示", "🔍")
        
        demo = VerificationDemo(mode=mode)
        results = await demo.run_all_demos()
        
        # 收集性能统计
        for result in results:
            if result["success"] and "verify_time_ms" in result:
                algo_name = result["algorithm"].split(" ")[0]  # 提取算法名称
                if algo_name not in self.performance_stats:
                    self.performance_stats[algo_name] = {"sign": [], "verify": []}
                self.performance_stats[algo_name]["verify"].append(result["verify_time_ms"])
        
        return results

    async def run_performance_demos(self, mode: str = "dev") -> List[Dict[str, Any]]:
        """运行性能测试演示"""
        self.print_section_header("性能测试和安全验证", "📊")

        demo = PerformanceDemo(mode=mode)
        results = await demo.run_all_performance_tests()

        # 收集性能统计
        for result in results:
            if result.get("test_type") == "加密算法性能基准测试":
                for algo_result in result.get("algorithms", []):
                    algo_name = algo_result["algorithm"]
                    if algo_name not in self.performance_stats:
                        self.performance_stats[algo_name] = {"encrypt": [], "decrypt": []}

                    # 收集各种大小的平均时间
                    for size_test in algo_result.get("size_tests", []):
                        if size_test.get("avg_time_ms"):
                            self.performance_stats[algo_name]["encrypt"].append(size_test["avg_time_ms"])

        return results

    def print_performance_summary(self):
        """打印性能统计摘要"""
        self.print_section_header("性能统计摘要", "📊")
        
        if not self.performance_stats:
            print("❌ 没有收集到性能数据")
            return
            
        print(f"{'算法':<15} {'操作':<10} {'平均时间(ms)':<15} {'最小时间(ms)':<15} {'最大时间(ms)':<15} {'样本数':<10}")
        print("-" * 80)
        
        for algo_name, operations in self.performance_stats.items():
            for op_name, times in operations.items():
                if times:
                    avg_time = sum(times) / len(times)
                    min_time = min(times)
                    max_time = max(times)
                    sample_count = len(times)
                    
                    print(f"{algo_name:<15} {op_name:<10} {avg_time:<15.2f} {min_time:<15.2f} {max_time:<15.2f} {sample_count:<10}")
        
        print()
        
    def print_overall_summary(self):
        """打印总体摘要"""
        self.print_section_header("演示总结", "🎯")
        
        if not self.all_results:
            print("❌ 没有演示结果")
            return
            
        total_demos = len(self.all_results)
        successful_demos = sum(1 for result in self.all_results if result.get("success", False))
        success_rate = (successful_demos / total_demos) * 100 if total_demos > 0 else 0
        
        total_time = (self.end_time - self.start_time) if self.start_time and self.end_time else 0
        
        print(f"📈 演示统计:")
        print(f"  • 总演示数量: {total_demos}")
        print(f"  • 成功演示数量: {successful_demos}")
        print(f"  • 失败演示数量: {total_demos - successful_demos}")
        print(f"  • 成功率: {success_rate:.1f}%")
        print(f"  • 总执行时间: {total_time:.2f} 秒")
        print()
        
        # 按类别统计
        categories = {}
        for result in self.all_results:
            category = result.get("category", "未知")
            if category not in categories:
                categories[category] = {"total": 0, "success": 0}
            categories[category]["total"] += 1
            if result.get("success", False):
                categories[category]["success"] += 1
        
        if categories:
            print("📊 分类统计:")
            for category, stats in categories.items():
                success_rate = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
                print(f"  • {category}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")
        
        print()
        
    def save_results_to_file(self):
        """保存结果到文件"""
        try:
            results_data = {
                "timestamp": datetime.now().isoformat(),
                "total_execution_time_seconds": (self.end_time - self.start_time) if self.start_time and self.end_time else 0,
                "performance_stats": self.performance_stats,
                "demo_results": self.all_results,
                "summary": {
                    "total_demos": len(self.all_results),
                    "successful_demos": sum(1 for r in self.all_results if r.get("success", False)),
                    "success_rate": (sum(1 for r in self.all_results if r.get("success", False)) / len(self.all_results)) * 100 if self.all_results else 0
                }
            }
            
            # 保存到JSON文件
            results_file = Path(__file__).parent / f"demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, ensure_ascii=False, indent=2)
            
            print(f"📁 演示结果已保存到: {results_file}")
            
        except Exception as e:
            print(f"❌ 保存结果文件失败: {e}")
            
    async def run_complete_demo(self, mode: str = "dev"):
        """运行完整演示"""
        self.start_time = time.time()
        
        try:
            # 运行各个模块的演示
            encryption_results = await self.run_encryption_demos(mode)
            for result in encryption_results:
                result["category"] = "加密"
                self.all_results.append(result)
            
            signature_results = await self.run_signature_demos(mode)
            for result in signature_results:
                result["category"] = "签名"
                self.all_results.append(result)
            
            decryption_results = await self.run_decryption_demos(mode)
            for result in decryption_results:
                result["category"] = "解密"
                self.all_results.append(result)
            
            verification_results = await self.run_verification_demos(mode)
            for result in verification_results:
                result["category"] = "验签"
                self.all_results.append(result)

            performance_results = await self.run_performance_demos(mode)
            for result in performance_results:
                result["category"] = "性能测试"
                self.all_results.append(result)

        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
        finally:
            self.end_time = time.time()
            
    async def run_mode_comparison(self):
        """运行模式对比演示"""
        self.print_section_header("开发模式 vs 生产模式对比", "⚙️")
        
        print("🔧 开发模式特点:")
        print("  • 使用模拟密钥和算法实现")
        print("  • 快速响应，适合开发和测试")
        print("  • 不依赖外部密钥管理系统")
        print("  • 提供详细的调试信息")
        print()
        
        print("🏭 生产模式特点:")
        print("  • 使用真实的密钥管理系统(Vault)")
        print("  • 严格的安全验证和审计")
        print("  • 真实的加密算法实现")
        print("  • 完整的错误处理和日志记录")
        print()
        
        print("💡 注意: 本演示主要在开发模式下运行，生产模式需要配置Vault等外部依赖")
        print()


async def main():
    """主函数"""
    demo = CryptoMainDemo()
    
    # 打印程序头部
    demo.print_header()
    
    # 运行完整演示
    print("🚀 开始运行完整的加密算法演示...")
    await demo.run_complete_demo(mode="dev")
    
    # 运行模式对比说明
    await demo.run_mode_comparison()
    
    # 打印性能统计
    demo.print_performance_summary()
    
    # 打印总体摘要
    demo.print_overall_summary()
    
    # 保存结果到文件
    demo.save_results_to_file()
    
    # 结束信息
    print("🎉 TestGenius 加密算法演示完成！")
    print("=" * 80)
    print("感谢使用 TestGenius 加密模块演示程序")
    print("如有问题，请查看演示结果文件或联系开发团队")
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())
