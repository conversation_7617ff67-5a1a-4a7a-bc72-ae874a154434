#!/usr/bin/env python3
"""
演示程序测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入"""
    try:
        # 测试基础模块导入
        from src.crypto.client import CryptoClient
        from src.crypto.models import CryptoRequest, CryptoOperation
        print("✅ 基础模块导入成功")
        
        # 测试演示模块导入
        from demos.crypto.encryption_demo import EncryptionDemo
        from demos.crypto.signature_demo import SignatureDemo
        from demos.crypto.main_demo import CryptoMainDemo
        print("✅ 演示模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crypto_client():
    """测试加密客户端"""
    try:
        from src.crypto.client import CryptoClient
        
        # 创建客户端
        client = CryptoClient(mode="dev")
        print("✅ 加密客户端创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 加密客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 演示程序测试开始")
    print("-" * 40)
    
    # 测试导入
    import_ok = test_imports()
    
    # 测试客户端
    client_ok = test_crypto_client()
    
    print("-" * 40)
    if import_ok and client_ok:
        print("🎉 基础测试通过")
        
        # 写入结果文件
        with open("demo_test_result.txt", "w", encoding="utf-8") as f:
            f.write("SUCCESS: 演示程序基础功能正常\n")
            f.write("- 模块导入: 成功\n")
            f.write("- 客户端创建: 成功\n")
            f.write("- 演示程序可以正常运行\n")
    else:
        print("❌ 基础测试失败")
        with open("demo_test_result.txt", "w", encoding="utf-8") as f:
            f.write("FAILED: 演示程序基础功能异常\n")
            f.write(f"- 模块导入: {'成功' if import_ok else '失败'}\n")
            f.write(f"- 客户端创建: {'成功' if client_ok else '失败'}\n")

if __name__ == "__main__":
    main()
