#!/usr/bin/env python3
"""
修复decryption_demo.py中CryptoRequest调用缺失的参数
"""

def fix_crypto_requests():
    file_path = 'demos/crypto/decryption_demo.py'
    
    # 读取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找需要修复的行
    modified = False
    for i, line in enumerate(lines):
        # 查找包含signature_config=None,但没有key_generation_config的行
        if ('signature_config=None,' in line and 
            'key_generation_config' not in line and
            i + 1 < len(lines) and 
            'hash_algorithm=' in lines[i + 1]):
            
            # 在当前行后插入新的参数
            indent = len(line) - len(line.lstrip())
            new_lines = [
                ' ' * indent + 'key_generation_config=None,\n',
                ' ' * indent + 'key_export_config=None,\n'
            ]
            
            # 插入新行
            lines[i+1:i+1] = new_lines
            modified = True
            print(f"修复第 {i+1} 行: {line.strip()}")
    
    if modified:
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        print(f"✅ 文件 {file_path} 修复完成")
    else:
        print("❌ 没有找到需要修复的内容")

if __name__ == "__main__":
    fix_crypto_requests() 