#!/usr/bin/env python3
"""
TestGenius 密钥生成与PEM转换演示模块

本模块展示密钥生成和PEM格式转换功能，包括：
- 对称密钥生成
- 非对称密钥对生成（RSA、ECDSA、SM2）
- 密钥PEM格式导出
- 密钥指纹计算
- Vault存储集成
"""

import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any, List
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    KeyGenerationConfig,
    KeyExportConfig,
    KeyType,
    KeyFormat,
    EncryptionAlgorithm,
    SignatureAlgorithm,
)


class KeyGenerationDemo:
    """密钥生成演示类"""
    
    def __init__(self, mode: str = "dev"):
        """
        初始化密钥生成演示
        
        Args:
            mode: 运行模式，'dev' 或 'production'
        """
        self.mode = mode
        self.crypto_client = CryptoClient(mode=self.mode)
        self.demo_results = []
        
    async def initialize(self):
        """初始化加密客户端"""
        print(f"🔧 初始化密钥生成客户端 (模式: {self.mode})")
        await self.crypto_client.initialize()
        print("✅ 密钥生成客户端初始化完成\n")
        
    async def cleanup(self):
        """清理资源"""
        if self.crypto_client:
            await self.crypto_client.cleanup()
            
    async def demo_symmetric_key_generation(self) -> Dict[str, Any]:
        """演示对称密钥生成"""
        print("🔑 对称密钥生成演示")
        print("-" * 50)
        
        # AES-256密钥生成
        print("📝 生成AES-256密钥")
        
        key_gen_config = KeyGenerationConfig(
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            key_id="demo-aes-256-key-001",
            store_in_vault=False  # 开发模式不存储到Vault
        )
        
        generate_request = CryptoRequest(
            operation=CryptoOperation.GENERATE_KEY,
            data="",
            key_generation_config=key_gen_config,
            key_export_config=None,
        )
        
        start_time = time.time()
        response = await self.crypto_client.process_request(generate_request)
        end_time = time.time()
        
        result = {
            "algorithm": "AES-256对称密钥",
            "success": response.success,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        
        if response.success:
            key_result = response.result
            result.update({
                "key_id": key_result.key_id,
                "key_type": key_result.key_type,
                "key_size": key_result.key_size,
                "fingerprint": key_result.fingerprint,
                "has_symmetric_key": bool(key_result.public_key_pem),
                "created_at": key_result.created_at,
                "stored_in_vault": key_result.stored_in_vault
            })
            
            print(f"✅ AES-256密钥生成成功")
            print(f"   密钥ID: {key_result.key_id}")
            print(f"   密钥大小: {key_result.key_size} bits")
            print(f"   指纹: {key_result.fingerprint}")
            print(f"   生成时间: {result['processing_time_ms']:.2f} ms")
            
            # 演示密钥导出
            print("\n📤 导出密钥为Base64格式")
            export_config = KeyExportConfig(
                key_id=key_result.key_id,
                export_format=KeyFormat.PEM,
                include_private=False
            )
            
            export_request = CryptoRequest(
                operation=CryptoOperation.EXPORT_KEY_PEM,
                data="",
                key_export_config=export_config,
                key_generation_config=None,
            )
            
            export_response = await self.crypto_client.process_request(export_request)
            if export_response.success:
                export_result = export_response.result
                result["export_success"] = True
                result["exported_key_length"] = len(export_result.get("symmetric_key", ""))
                print(f"✅ 密钥导出成功，长度: {result['exported_key_length']} 字符")
            else:
                result["export_success"] = False
                print(f"❌ 密钥导出失败: {export_response.error_message}")
                
        else:
            result["error"] = response.error_message
            print(f"❌ AES-256密钥生成失败: {response.error_message}")
            
        print()
        return result
        
    async def demo_rsa_key_generation(self) -> Dict[str, Any]:
        """演示RSA密钥对生成"""
        print("🔐 RSA密钥对生成演示")
        print("-" * 50)
        
        # RSA-2048密钥对生成
        print("📝 生成RSA-2048密钥对")
        
        key_gen_config = KeyGenerationConfig(
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.RSA_PSS,
            key_size=2048,
            key_id="demo-rsa-2048-keypair-001",
            public_exponent=65537,
            store_in_vault=False
        )
        
        generate_request = CryptoRequest(
            operation=CryptoOperation.GENERATE_KEY,
            data="",
            key_generation_config=key_gen_config,
            key_export_config=None,
        )
        
        start_time = time.time()
        response = await self.crypto_client.process_request(generate_request)
        end_time = time.time()
        
        result = {
            "algorithm": "RSA-2048密钥对",
            "success": response.success,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        
        if response.success:
            key_result = response.result
            result.update({
                "key_id": key_result.key_id,
                "key_type": key_result.key_type,
                "key_size": key_result.key_size,
                "fingerprint": key_result.fingerprint,
                "has_public_key": bool(key_result.public_key_pem),
                "has_private_key": bool(key_result.private_key_pem),
                "public_key_length": len(key_result.public_key_pem or ""),
                "private_key_length": len(key_result.private_key_pem or ""),
                "created_at": key_result.created_at,
                "stored_in_vault": key_result.stored_in_vault
            })
            
            print(f"✅ RSA-2048密钥对生成成功")
            print(f"   密钥ID: {key_result.key_id}")
            print(f"   密钥大小: {key_result.key_size} bits")
            print(f"   指纹: {key_result.fingerprint}")
            print(f"   公钥长度: {result['public_key_length']} 字符")
            print(f"   私钥长度: {result['private_key_length']} 字符")
            print(f"   生成时间: {result['processing_time_ms']:.2f} ms")
            
            # 显示PEM格式示例（前50字符）
            if key_result.public_key_pem:
                print(f"   公钥PEM开头: {key_result.public_key_pem[:50]}...")
            if key_result.private_key_pem:
                print(f"   私钥PEM开头: {key_result.private_key_pem[:50]}...")
                
            # 演示仅导出公钥
            print("\n📤 导出公钥PEM格式")
            export_config = KeyExportConfig(
                key_id=key_result.key_id,
                export_format=KeyFormat.PEM,
                include_private=False  # 仅导出公钥
            )
            
            export_request = CryptoRequest(
                operation=CryptoOperation.EXPORT_KEY_PEM,
                data="",
                key_export_config=export_config,
                key_generation_config=None,
            )
            
            export_response = await self.crypto_client.process_request(export_request)
            if export_response.success:
                export_result = export_response.result
                result["export_success"] = True
                result["exported_public_key_length"] = len(export_result.get("public_key_pem", ""))
                print(f"✅ 公钥导出成功，长度: {result['exported_public_key_length']} 字符")
            else:
                result["export_success"] = False
                print(f"❌ 公钥导出失败: {export_response.error_message}")
                
        else:
            result["error"] = response.error_message
            print(f"❌ RSA-2048密钥对生成失败: {response.error_message}")
            
        print()
        return result
        
    async def demo_ecdsa_key_generation(self) -> Dict[str, Any]:
        """演示ECDSA密钥对生成"""
        print("🔏 ECDSA密钥对生成演示")
        print("-" * 50)
        
        # ECDSA P-256密钥对生成
        print("📝 生成ECDSA P-256密钥对")
        
        key_gen_config = KeyGenerationConfig(
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.ECDSA,
            key_size=256,  # P-256曲线
            key_id="demo-ecdsa-p256-keypair-001",
            curve_name="secp256r1",
            store_in_vault=False
        )
        
        generate_request = CryptoRequest(
            operation=CryptoOperation.GENERATE_KEY,
            data="",
            key_generation_config=key_gen_config,
            key_export_config=None,
        )
        
        start_time = time.time()
        response = await self.crypto_client.process_request(generate_request)
        end_time = time.time()
        
        result = {
            "algorithm": "ECDSA P-256密钥对",
            "success": response.success,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        
        if response.success:
            key_result = response.result
            result.update({
                "key_id": key_result.key_id,
                "key_type": key_result.key_type,
                "key_size": key_result.key_size,
                "fingerprint": key_result.fingerprint,
                "has_public_key": bool(key_result.public_key_pem),
                "has_private_key": bool(key_result.private_key_pem),
                "public_key_length": len(key_result.public_key_pem or ""),
                "private_key_length": len(key_result.private_key_pem or ""),
                "created_at": key_result.created_at,
                "stored_in_vault": key_result.stored_in_vault
            })
            
            print(f"✅ ECDSA P-256密钥对生成成功")
            print(f"   密钥ID: {key_result.key_id}")
            print(f"   曲线: P-{key_result.key_size}")
            print(f"   指纹: {key_result.fingerprint}")
            print(f"   公钥长度: {result['public_key_length']} 字符")
            print(f"   私钥长度: {result['private_key_length']} 字符")
            print(f"   生成时间: {result['processing_time_ms']:.2f} ms")
            
            # 显示PEM格式示例（前50字符）
            if key_result.public_key_pem:
                print(f"   公钥PEM开头: {key_result.public_key_pem[:50]}...")
                
        else:
            result["error"] = response.error_message
            print(f"❌ ECDSA P-256密钥对生成失败: {response.error_message}")
            
        print()
        return result
        
    async def demo_sm2_key_generation(self) -> Dict[str, Any]:
        """演示SM2密钥对生成（国密）"""
        print("🇨🇳 SM2密钥对生成演示（国密算法）")
        print("-" * 50)
        
        # SM2密钥对生成
        print("📝 生成SM2密钥对")
        
        key_gen_config = KeyGenerationConfig(
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.SM2,
            key_size=256,  # SM2固定为256位
            key_id="demo-sm2-keypair-001",
            store_in_vault=False
        )
        
        generate_request = CryptoRequest(
            operation=CryptoOperation.GENERATE_KEY,
            data="",
            key_generation_config=key_gen_config,
            key_export_config=None,
        )
        
        start_time = time.time()
        response = await self.crypto_client.process_request(generate_request)
        end_time = time.time()
        
        result = {
            "algorithm": "SM2密钥对（国密）",
            "success": response.success,
            "processing_time_ms": (end_time - start_time) * 1000,
            "mode_info": f"运行模式: {self.mode}"
        }
        
        if response.success:
            key_result = response.result
            result.update({
                "key_id": key_result.key_id,
                "key_type": key_result.key_type,
                "key_size": key_result.key_size,
                "fingerprint": key_result.fingerprint,
                "has_public_key": bool(key_result.public_key_pem),
                "has_private_key": bool(key_result.private_key_pem),
                "public_key_length": len(key_result.public_key_pem or ""),
                "private_key_length": len(key_result.private_key_pem or ""),
                "created_at": key_result.created_at,
                "stored_in_vault": key_result.stored_in_vault
            })
            
            print(f"✅ SM2密钥对生成成功")
            print(f"   密钥ID: {key_result.key_id}")
            print(f"   国密算法: SM2")
            print(f"   指纹: {key_result.fingerprint}")
            print(f"   公钥长度: {result['public_key_length']} 字符")
            print(f"   私钥长度: {result['private_key_length']} 字符")
            print(f"   生成时间: {result['processing_time_ms']:.2f} ms")
            
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM2使用ECDSA P-256模拟实现")
                
        else:
            result["error"] = response.error_message
            print(f"❌ SM2密钥对生成失败: {response.error_message}")
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM2使用模拟实现")
            
        print()
        return result
        
    async def run_all_demos(self) -> List[Dict[str, Any]]:
        """运行所有密钥生成演示"""
        print("🚀 开始密钥生成与PEM转换演示")
        print("=" * 60)
        print(f"运行模式: {self.mode.upper()}")
        print("=" * 60)
        print()
        
        await self.initialize()
        
        try:
            # 运行各种密钥生成演示
            demos = [
                self.demo_symmetric_key_generation(),
                self.demo_rsa_key_generation(),
                self.demo_ecdsa_key_generation(),
                self.demo_sm2_key_generation(),
            ]
            
            results = []
            for demo in demos:
                result = await demo
                results.append(result)
                self.demo_results.append(result)
                
            return results
            
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("TestGenius 密钥生成与PEM转换演示程序")
    print("=" * 60)
    
    # 开发模式演示
    print("\n🔧 开发模式演示")
    dev_demo = KeyGenerationDemo(mode="dev")
    dev_results = await dev_demo.run_all_demos()
    
    # 保存结果到文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"key_generation_results_{timestamp}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "demo_type": "key_generation",
            "total_demos": len(dev_results),
            "successful_demos": sum(1 for r in dev_results if r.get("success", False)),
            "results": dev_results
        }, f, indent=2, ensure_ascii=False, default=str)
    
    # 显示演示结果摘要
    print("\n📊 演示结果摘要")
    print("-" * 50)
    for i, result in enumerate(dev_results, 1):
        status = "✅ 成功" if result["success"] else "❌ 失败"
        time_info = f"{result.get('processing_time_ms', 0):.2f}ms"
        
        extra_info = ""
        if result.get("has_public_key") and result.get("has_private_key"):
            extra_info = " (密钥对)"
        elif result.get("has_symmetric_key"):
            extra_info = " (对称密钥)"
        
        print(f"{i}. {result['algorithm']}: {status} ({time_info}){extra_info}")
        
        if not result["success"]:
            print(f"   错误: {result.get('error', '未知错误')}")
    
    print(f"\n💾 详细结果已保存到: {results_file}")
    print("\n🎉 密钥生成演示完成！")


if __name__ == "__main__":
    asyncio.run(main()) 