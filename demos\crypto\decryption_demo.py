"""
TestGenius 解密演示模块

本模块展示各种解密算法的使用方法，包括：
- 对应加密演示中各种加密数据的解密
- 验证解密结果的正确性
- 处理解密失败的异常情况
- 完整的加密-解密往返验证
"""

import asyncio
import time
import tempfile
import os
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    EncryptionConfig,
    KeyConfig,
    KeyType,
    EncryptionAlgorithm,
    HashAlgorithm,
)


class DecryptionDemo:
    """解密演示类"""
    
    def __init__(self, mode: str = "dev"):
        """
        初始化解密演示
        
        Args:
            mode: 运行模式，'dev' 或 'production'
        """
        self.mode = mode
        self.crypto_client: Optional[CryptoClient] = None
        self.demo_results: List[Dict[str, Any]] = []
        
    async def initialize(self):
        """初始化加密客户端"""
        print(f"🔧 初始化解密客户端 (模式: {self.mode})")
        self.crypto_client = CryptoClient(mode=self.mode)
        await self.crypto_client.initialize()
        print("✅ 解密客户端初始化完成\n")
        
    async def cleanup(self):
        """清理资源"""
        if self.crypto_client:
            await self.crypto_client.cleanup()
            
    async def demo_aes_gcm_round_trip(self) -> Dict[str, Any]:
        """演示AES-GCM加密解密往返"""
        print("🔄 AES-GCM 加密解密往返演示")
        print("-" * 50)
        
        # 原始测试数据
        original_data = "这是AES-GCM加密解密往返测试的原始数据，包含中文字符和特殊符号!@#$%^&*()"
        print(f"原始数据: {original_data}")
        
        # 配置密钥
        key_config = KeyConfig(
            key_id="demo-aes-gcm-roundtrip-key-001",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            vault_path="secret/crypto/demo",
            environment="demo"
        )
        
        # 配置加密参数
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config,
            mode="GCM",
            padding="NONE",
            iv_length=12,
            tag_length=16
        )
        
        # 第一步：加密
        print("🔐 步骤1: 加密数据")
        encrypt_request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data=original_data,
            encryption_config=encryption_config,
            signature_config=None,
            key_generation_config=None,
            key_export_config=None,
            hash_algorithm=None,
            encoding="utf-8",
            output_format="base64"
        )
        
        encrypt_start_time = time.time()
        if self.crypto_client is None:
            raise RuntimeError("Crypto client not initialized")
        encrypt_response = await self.crypto_client.process_request(encrypt_request)
        encrypt_end_time = time.time()
        
        if not encrypt_response.success:
            result = {
                "algorithm": "AES-GCM (往返)",
                "success": False,
                "error": f"加密失败: {encrypt_response.error_message}",
                "encrypt_time_ms": (encrypt_end_time - encrypt_start_time) * 1000
            }
            print(f"❌ 加密失败: {encrypt_response.error_message}")
            return result
            
        encrypted_data = encrypt_response.result
        if not isinstance(encrypted_data, str):
            raise TypeError("Expected string result from encryption")
        print(f"✅ 加密成功: {encrypted_data[:50]}...")
        print(f"加密时间: {(encrypt_end_time - encrypt_start_time) * 1000:.2f} ms")
        
        # 第二步：解密
        print("\n🔓 步骤2: 解密数据")
        decrypt_request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data=encrypted_data,
            encryption_config=encryption_config,
            signature_config=None,
            key_generation_config=None,
            key_export_config=None,
            hash_algorithm=None,
            encoding="utf-8",
            output_format="text"
        )
        
        decrypt_start_time = time.time()
        decrypt_response = await self.crypto_client.process_request(decrypt_request)
        decrypt_end_time = time.time()
        
        result = {
            "algorithm": "AES-GCM (往返)",
            "success": decrypt_response.success,
            "original_data": original_data,
            "encrypted_data": encrypted_data,
            "decrypted_data": decrypt_response.result if decrypt_response.success else None,
            "data_integrity_verified": False,
            "encrypt_time_ms": (encrypt_end_time - encrypt_start_time) * 1000,
            "decrypt_time_ms": (decrypt_end_time - decrypt_start_time) * 1000,
            "total_time_ms": (encrypt_end_time - encrypt_start_time + decrypt_end_time - decrypt_start_time) * 1000,
            "error": decrypt_response.error_message if not decrypt_response.success else None
        }
        
        if decrypt_response.success:
            decrypted_data = decrypt_response.result
            if isinstance(decrypted_data, str):
                print(f"✅ 解密成功: {decrypted_data}")
                print(f"解密时间: {result['decrypt_time_ms']:.2f} ms")
                
                # 验证数据完整性
                if original_data == decrypted_data:
                    result["data_integrity_verified"] = True
                    print("✅ 数据完整性验证通过")
                else:
                    print("❌ 数据完整性验证失败")
                    print(f"原始数据长度: {len(original_data)}")
                    print(f"解密数据长度: {len(decrypted_data)}")
            else:
                print("❌ 解密结果类型错误")
        else:
            print(f"❌ 解密失败: {decrypt_response.error_message}")
            
        print(f"总处理时间: {result['total_time_ms']:.2f} ms")
        print()
        return result
        
    async def demo_sm4_round_trip(self) -> Dict[str, Any]:
        """演示SM4加密解密往返"""
        print("🔄 SM4 国密加密解密往返演示")
        print("-" * 50)
        
        # 原始测试数据
        original_data = "SM4国密算法加密解密往返测试数据 - TestGenius项目安全模块演示"
        print(f"原始数据: {original_data}")
        
        # 配置密钥
        key_config = KeyConfig(
            key_id="demo-sm4-roundtrip-key-001",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.SM4,
            key_size=128,
            vault_path="secret/crypto/demo",
            environment="demo"
        )
        
        # 配置加密参数
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.SM4,
            key_config=key_config,
            mode="CBC",
            padding="PKCS7",
            iv_length=16,
            tag_length=16
        )
        
        # 第一步：加密
        print("🔐 步骤1: SM4加密")
        encrypt_request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data=original_data,
            encryption_config=encryption_config,
            signature_config=None,
            key_generation_config=None,
            key_export_config=None,
            hash_algorithm=None,
            encoding="utf-8",
            output_format="base64"
        )
        
        encrypt_start_time = time.time()
        if self.crypto_client is None:
            raise RuntimeError("Crypto client not initialized")
        encrypt_response = await self.crypto_client.process_request(encrypt_request)
        encrypt_end_time = time.time()
        
        if not encrypt_response.success:
            result = {
                "algorithm": "SM4 (往返)",
                "success": False,
                "error": f"SM4加密失败: {encrypt_response.error_message}",
                "encrypt_time_ms": (encrypt_end_time - encrypt_start_time) * 1000,
                "mode_info": f"运行模式: {self.mode}"
            }
            print(f"❌ SM4加密失败: {encrypt_response.error_message}")
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM4可能使用模拟实现")
            return result
            
        encrypted_data = encrypt_response.result
        if not isinstance(encrypted_data, str):
            raise TypeError("Expected string result from encryption")
        print(f"✅ SM4加密成功: {encrypted_data[:50]}...")
        print(f"加密时间: {(encrypt_end_time - encrypt_start_time) * 1000:.2f} ms")
        
        # 第二步：解密
        print("\n🔓 步骤2: SM4解密")
        decrypt_request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data=encrypted_data,
            encryption_config=encryption_config,
            signature_config=None,
            key_generation_config=None,
            key_export_config=None,
            hash_algorithm=None,
            encoding="utf-8",
            output_format="text"
        )
        
        decrypt_start_time = time.time()
        decrypt_response = await self.crypto_client.process_request(decrypt_request)
        decrypt_end_time = time.time()
        
        result = {
            "algorithm": "SM4 (往返)",
            "success": decrypt_response.success,
            "original_data": original_data,
            "encrypted_data": encrypted_data,
            "decrypted_data": decrypt_response.result if decrypt_response.success else None,
            "data_integrity_verified": False,
            "encrypt_time_ms": (encrypt_end_time - encrypt_start_time) * 1000,
            "decrypt_time_ms": (decrypt_end_time - decrypt_start_time) * 1000,
            "total_time_ms": (encrypt_end_time - encrypt_start_time + decrypt_end_time - decrypt_start_time) * 1000,
            "error": decrypt_response.error_message if not decrypt_response.success else None,
            "mode_info": f"运行模式: {self.mode}"
        }
        
        if decrypt_response.success:
            decrypted_data = decrypt_response.result
            if isinstance(decrypted_data, str):
                print(f"✅ SM4解密成功: {decrypted_data}")
                print(f"解密时间: {result['decrypt_time_ms']:.2f} ms")
                
                # 验证数据完整性
                if original_data == decrypted_data:
                    result["data_integrity_verified"] = True
                    print("✅ SM4数据完整性验证通过")
                else:
                    print("❌ SM4数据完整性验证失败")
            else:
                print("❌ SM4解密结果类型错误")
        else:
            print(f"❌ SM4解密失败: {decrypt_response.error_message}")
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM4可能使用模拟实现")
            
        print(f"总处理时间: {result['total_time_ms']:.2f} ms")
        print()
        return result
        
    async def demo_decryption_error_handling(self) -> Dict[str, Any]:
        """演示解密错误处理"""
        print("⚠️ 解密错误处理演示")
        print("-" * 50)
        
        # 配置密钥
        key_config = KeyConfig(
            key_id="demo-error-handling-key-001",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            vault_path="secret/crypto/demo",
            environment="demo"
        )
        
        # 配置加密参数
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config,
            mode="GCM",
            padding="NONE",
            iv_length=12,
            tag_length=16
        )
        
        error_cases = []
        
        if self.crypto_client is None:
            raise RuntimeError("Crypto client not initialized")
        
        # 错误案例1: 无效的Base64数据
        print("🧪 测试案例1: 无效的Base64加密数据")
        invalid_base64_data = "这不是有效的Base64编码数据!@#$%"
        
        decrypt_request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data=invalid_base64_data,
            encryption_config=encryption_config,
            signature_config=None,
            key_generation_config=None,
            key_export_config=None,
            hash_algorithm=None,
            encoding="utf-8",
            output_format="text"
        )
        
        start_time = time.time()
        response = await self.crypto_client.process_request(decrypt_request)
        end_time = time.time()
        
        case1_result = {
            "case": "无效Base64数据",
            "input_data": invalid_base64_data,
            "success": response.success,
            "error_message": response.error_message if not response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        error_cases.append(case1_result)
        
        status1 = "✅ 正确处理错误" if not response.success else "❌ 应该失败但成功了"
        print(f"{status1}: {response.error_message if not response.success else '意外成功'}")
        
        # 错误案例2: 空数据
        print("\n🧪 测试案例2: 空加密数据")
        empty_data = ""
        
        decrypt_request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data=empty_data,
            encryption_config=encryption_config,
            signature_config=None,
            key_generation_config=None,
            key_export_config=None,
            hash_algorithm=None,
            encoding="utf-8",
            output_format="text"
        )
        
        start_time = time.time()
        response = await self.crypto_client.process_request(decrypt_request)
        end_time = time.time()
        
        case2_result = {
            "case": "空数据",
            "input_data": empty_data,
            "success": response.success,
            "error_message": response.error_message if not response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        error_cases.append(case2_result)
        
        status2 = "✅ 正确处理错误" if not response.success else "❌ 应该失败但成功了"
        print(f"{status2}: {response.error_message if not response.success else '意外成功'}")
        
        # 错误案例3: 错误的密钥ID
        print("\n🧪 测试案例3: 错误的密钥ID")
        wrong_key_config = KeyConfig(
            key_id="wrong-key-id-that-does-not-exist",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            vault_path="secret/crypto/demo",
            environment="demo"
        )
        
        wrong_encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=wrong_key_config,
            mode="GCM",
            padding="NONE",
            iv_length=12,
            tag_length=16
        )
        
        # 使用有效的Base64数据但错误的密钥
        fake_encrypted_data = "dGVzdCBkYXRhIGZvciBkZWNyeXB0aW9uIGVycm9yIHRlc3Rpbmc="  # "test data for decryption error testing" in base64
        
        decrypt_request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data=fake_encrypted_data,
            encryption_config=wrong_encryption_config,
            signature_config=None,
            key_generation_config=None,
            key_export_config=None,
            hash_algorithm=None,
            encoding="utf-8",
            output_format="text"
        )
        
        start_time = time.time()
        response = await self.crypto_client.process_request(decrypt_request)
        end_time = time.time()
        
        case3_result = {
            "case": "错误密钥ID",
            "input_data": fake_encrypted_data,
            "key_id": wrong_key_config.key_id,
            "success": response.success,
            "error_message": response.error_message if not response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        error_cases.append(case3_result)
        
        status3 = "✅ 正确处理错误" if not response.success else "❌ 应该失败但成功了"
        print(f"{status3}: {response.error_message if not response.success else '意外成功'}")
        
        # 统计结果
        total_cases = len(error_cases)
        correctly_handled = sum(1 for case in error_cases if not case["success"])
        
        result = {
            "algorithm": "错误处理测试",
            "total_test_cases": total_cases,
            "correctly_handled_errors": correctly_handled,
            "incorrectly_handled_errors": total_cases - correctly_handled,
            "error_handling_rate": (correctly_handled / total_cases) * 100,
            "test_cases": error_cases,
            "overall_success": correctly_handled == total_cases
        }
        
        print(f"\n📊 错误处理测试统计:")
        print(f"  总测试案例: {result['total_test_cases']}")
        print(f"  正确处理错误: {result['correctly_handled_errors']}")
        print(f"  错误处理率: {result['error_handling_rate']:.1f}%")
        
        print()
        return result
        
    async def run_all_demos(self) -> List[Dict[str, Any]]:
        """运行所有解密演示"""
        print("🚀 开始解密演示")
        print("=" * 60)
        print(f"运行模式: {self.mode.upper()}")
        print("=" * 60)
        print()
        
        await self.initialize()
        
        try:
            # 运行各种解密演示
            demos = [
                self.demo_aes_gcm_round_trip(),
                self.demo_sm4_round_trip(),
                self.demo_decryption_error_handling(),
            ]
            
            results = []
            for demo in demos:
                result = await demo
                results.append(result)
                self.demo_results.append(result)
                
            return results
            
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("TestGenius 解密演示程序")
    print("=" * 60)
    
    # 开发模式演示
    print("\n🔧 开发模式演示")
    dev_demo = DecryptionDemo(mode="dev")
    dev_results = await dev_demo.run_all_demos()
    
    # 显示演示结果摘要
    print("\n📊 演示结果摘要")
    print("-" * 50)
    for i, result in enumerate(dev_results, 1):
        if "total_test_cases" in result:  # 错误处理测试结果
            status = f"✅ {result['correctly_handled_errors']}/{result['total_test_cases']} 正确处理"
            time_info = "错误处理测试"
        elif "data_integrity_verified" in result:  # 往返测试结果
            integrity_status = "✅ 完整" if result.get("data_integrity_verified") else "❌ 损坏"
            status = f"✅ 成功 (数据{integrity_status})" if result["success"] else "❌ 失败"
            time_info = f"{result.get('total_time_ms', 0):.2f}ms"
        else:
            status = "✅ 成功" if result["success"] else "❌ 失败"
            time_info = f"{result.get('processing_time_ms', 0):.2f}ms"
        print(f"{i}. {result['algorithm']}: {status} ({time_info})")
        
    print("\n🎉 解密演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
