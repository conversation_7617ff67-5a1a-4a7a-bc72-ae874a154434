"""
TestGenius 性能测试和安全验证演示模块

本模块展示加密算法的性能测试和安全验证，包括：
- 各种算法的性能基准测试
- 大数据量处理性能测试
- 并发处理性能测试
- 安全性验证测试
- 开发模式和生产模式的性能对比
"""

import asyncio
import time
import statistics
import random
import string
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import Dict, Any, List, Tuple
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    EncryptionConfig,
    SignatureConfig,
    KeyConfig,
    KeyType,
    EncryptionAlgorithm,
    SignatureAlgorithm,
    HashAlgorithm,
)


class PerformanceDemo:
    """性能测试和安全验证演示类"""
    
    def __init__(self, mode: str = "dev"):
        """
        初始化性能测试演示
        
        Args:
            mode: 运行模式，'dev' 或 'production'
        """
        self.mode = mode
        self.crypto_client = None
        self.performance_results = []
        
    async def initialize(self):
        """初始化加密客户端"""
        print(f"🔧 初始化性能测试客户端 (模式: {self.mode})")
        self.crypto_client = CryptoClient(mode=self.mode)
        await self.crypto_client.initialize()
        print("✅ 性能测试客户端初始化完成\n")
        
    async def cleanup(self):
        """清理资源"""
        if self.crypto_client:
            await self.crypto_client.cleanup()
            
    def generate_test_data(self, size_bytes: int) -> str:
        """生成指定大小的测试数据"""
        # 生成随机字符串
        chars = string.ascii_letters + string.digits + "!@#$%^&*()_+-=[]{}|;:,.<>?"
        return ''.join(random.choice(chars) for _ in range(size_bytes))
        
    async def benchmark_encryption_algorithms(self) -> Dict[str, Any]:
        """基准测试各种加密算法"""
        print("🏃‍♂️ 加密算法性能基准测试")
        print("-" * 50)
        
        # 测试数据大小（字节）
        test_sizes = [100, 1024, 10240, 102400]  # 100B, 1KB, 10KB, 100KB
        
        # 测试算法配置
        algorithms = [
            {
                "name": "AES-GCM",
                "config": EncryptionConfig(
                    algorithm=EncryptionAlgorithm.AES_GCM,
                    key_config=KeyConfig(
                        key_id="perf-test-aes-gcm-key",
                        key_type=KeyType.SYMMETRIC,
                        algorithm=EncryptionAlgorithm.AES_GCM,
                        key_size=256,
                        environment="performance_test"
                    ),
                    iv_length=12,
                    tag_length=16
                )
            },
            {
                "name": "SM4",
                "config": EncryptionConfig(
                    algorithm=EncryptionAlgorithm.SM4,
                    key_config=KeyConfig(
                        key_id="perf-test-sm4-key",
                        key_type=KeyType.SYMMETRIC,
                        algorithm=EncryptionAlgorithm.SM4,
                        key_size=128,
                        environment="performance_test"
                    ),
                    mode="CBC",
                    iv_length=16
                )
            }
        ]
        
        benchmark_results = []
        
        for algo in algorithms:
            print(f"\n🔐 测试算法: {algo['name']}")
            algo_results = {"algorithm": algo["name"], "size_tests": []}
            
            for size in test_sizes:
                print(f"  📏 数据大小: {size} 字节")
                
                # 生成测试数据
                test_data = self.generate_test_data(size)
                
                # 执行多次测试取平均值
                times = []
                successful_tests = 0
                
                for i in range(5):  # 每个大小测试5次
                    request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
                        data=test_data,
                        encryption_config=algo["config"],
                        encoding="utf-8",
                        output_format="base64",
                        key_generation_config=None,
                        key_export_config=None,
)
                    
                    start_time = time.time()
                    response = await self.crypto_client.process_request(request)
                    end_time = time.time()
                    
                    if response.success:
                        processing_time = (end_time - start_time) * 1000  # 转换为毫秒
                        times.append(processing_time)
                        successful_tests += 1
                
                if times:
                    avg_time = statistics.mean(times)
                    min_time = min(times)
                    max_time = max(times)
                    std_dev = statistics.stdev(times) if len(times) > 1 else 0
                    throughput = (size / (avg_time / 1000)) / 1024  # KB/s
                    
                    size_result = {
                        "data_size_bytes": size,
                        "successful_tests": successful_tests,
                        "total_tests": 5,
                        "avg_time_ms": avg_time,
                        "min_time_ms": min_time,
                        "max_time_ms": max_time,
                        "std_dev_ms": std_dev,
                        "throughput_kbps": throughput
                    }
                    
                    algo_results["size_tests"].append(size_result)
                    
                    print(f"    ⏱️  平均时间: {avg_time:.2f} ms")
                    print(f"    📈 吞吐量: {throughput:.2f} KB/s")
                    print(f"    ✅ 成功率: {successful_tests}/5")
                else:
                    print(f"    ❌ 所有测试都失败了")
            
            benchmark_results.append(algo_results)
        
        result = {
            "test_type": "加密算法性能基准测试",
            "mode": self.mode,
            "algorithms": benchmark_results,
            "test_sizes_bytes": test_sizes
        }
        
        print(f"\n📊 基准测试完成")
        return result
        
    async def benchmark_signature_algorithms(self) -> Dict[str, Any]:
        """基准测试数字签名算法"""
        print("\n✍️ 数字签名算法性能基准测试")
        print("-" * 50)
        
        # 测试数据大小
        test_sizes = [100, 1024, 10240]  # 签名通常用于较小的数据
        
        # 测试算法配置
        algorithms = [
            {
                "name": "SM2",
                "config": SignatureConfig(
                    algorithm=SignatureAlgorithm.SM2,
                    hash_algorithm=HashAlgorithm.SM3,
                    key_config=KeyConfig(
                        key_id="perf-test-sm2-key",
                        key_type=KeyType.ASYMMETRIC_PRIVATE,
                        algorithm=SignatureAlgorithm.SM2,
                        key_size=256,
                        environment="performance_test"
                    )
                )
            },
            {
                "name": "RSA-PSS",
                "config": SignatureConfig(
                    algorithm=SignatureAlgorithm.RSA_PSS,
                    hash_algorithm=HashAlgorithm.SHA256,
                    key_config=KeyConfig(
                        key_id="perf-test-rsa-pss-key",
                        key_type=KeyType.ASYMMETRIC_PRIVATE,
                        algorithm=SignatureAlgorithm.RSA_PSS,
                        key_size=2048,
                        environment="performance_test"
                    ),
                    salt_length=32
                )
            }
        ]
        
        benchmark_results = []
        
        for algo in algorithms:
            print(f"\n✍️ 测试算法: {algo['name']}")
            algo_results = {"algorithm": algo["name"], "size_tests": []}
            
            for size in test_sizes:
                print(f"  📏 数据大小: {size} 字节")
                
                # 生成测试数据
                test_data = self.generate_test_data(size)
                
                # 执行多次测试取平均值
                times = []
                successful_tests = 0
                
                for i in range(3):  # 签名测试3次（比加密少，因为通常较慢）
                    request = CryptoRequest(
operation=CryptoOperation.SIGN,
                        data=test_data,
                        signature_config=algo["config"],
                        encoding="utf-8",
                        output_format="base64",
                        key_generation_config=None,
                        key_export_config=None,
)
                    
                    start_time = time.time()
                    response = await self.crypto_client.process_request(request)
                    end_time = time.time()
                    
                    if response.success:
                        processing_time = (end_time - start_time) * 1000
                        times.append(processing_time)
                        successful_tests += 1
                
                if times:
                    avg_time = statistics.mean(times)
                    min_time = min(times)
                    max_time = max(times)
                    std_dev = statistics.stdev(times) if len(times) > 1 else 0
                    
                    size_result = {
                        "data_size_bytes": size,
                        "successful_tests": successful_tests,
                        "total_tests": 3,
                        "avg_time_ms": avg_time,
                        "min_time_ms": min_time,
                        "max_time_ms": max_time,
                        "std_dev_ms": std_dev
                    }
                    
                    algo_results["size_tests"].append(size_result)
                    
                    print(f"    ⏱️  平均时间: {avg_time:.2f} ms")
                    print(f"    ✅ 成功率: {successful_tests}/3")
                else:
                    print(f"    ❌ 所有测试都失败了")
            
            benchmark_results.append(algo_results)
        
        result = {
            "test_type": "数字签名算法性能基准测试",
            "mode": self.mode,
            "algorithms": benchmark_results,
            "test_sizes_bytes": test_sizes
        }
        
        print(f"\n📊 签名基准测试完成")
        return result
        
    async def stress_test_large_data(self) -> Dict[str, Any]:
        """大数据量压力测试"""
        print("\n💪 大数据量压力测试")
        print("-" * 50)
        
        # 测试大数据量（1MB, 5MB, 10MB）
        large_sizes = [1024*1024, 5*1024*1024, 10*1024*1024]  # 1MB, 5MB, 10MB
        
        # 使用AES-GCM进行大数据测试
        key_config = KeyConfig(
            key_id="stress-test-aes-gcm-key",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            environment="stress_test"
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config,
            iv_length=12,
            tag_length=16
        )
        
        stress_results = []
        
        for size in large_sizes:
            size_mb = size / (1024 * 1024)
            print(f"\n📦 测试数据大小: {size_mb:.1f} MB")
            
            # 生成大量测试数据
            print("  🔄 生成测试数据...")
            data_gen_start = time.time()
            test_data = self.generate_test_data(size)
            data_gen_time = (time.time() - data_gen_start) * 1000
            print(f"  ✅ 数据生成完成 ({data_gen_time:.2f} ms)")
            
            # 执行加密测试
            request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
                data=test_data,
                encryption_config=encryption_config,
                encoding="utf-8",
                output_format="base64",
                key_generation_config=None,
                key_export_config=None,
)
            
            print("  🔐 执行加密...")
            encrypt_start = time.time()
            response = await self.crypto_client.process_request(request)
            encrypt_end = time.time()
            
            encrypt_time = (encrypt_end - encrypt_start) * 1000
            
            if response.success:
                throughput = (size / ((encrypt_time) / 1000)) / (1024 * 1024)  # MB/s
                
                stress_result = {
                    "data_size_mb": size_mb,
                    "data_generation_time_ms": data_gen_time,
                    "encryption_time_ms": encrypt_time,
                    "total_time_ms": data_gen_time + encrypt_time,
                    "throughput_mbps": throughput,
                    "success": True
                }
                
                print(f"  ✅ 加密成功")
                print(f"  ⏱️  加密时间: {encrypt_time:.2f} ms")
                print(f"  📈 吞吐量: {throughput:.2f} MB/s")
            else:
                stress_result = {
                    "data_size_mb": size_mb,
                    "data_generation_time_ms": data_gen_time,
                    "encryption_time_ms": encrypt_time,
                    "total_time_ms": data_gen_time + encrypt_time,
                    "success": False,
                    "error": response.error_message
                }
                
                print(f"  ❌ 加密失败: {response.error_message}")
            
            stress_results.append(stress_result)
        
        result = {
            "test_type": "大数据量压力测试",
            "mode": self.mode,
            "algorithm": "AES-GCM",
            "test_results": stress_results
        }
        
        print(f"\n📊 压力测试完成")
        return result
        
    async def security_validation_test(self) -> Dict[str, Any]:
        """安全性验证测试"""
        print("\n🔒 安全性验证测试")
        print("-" * 50)
        
        security_tests = []
        
        # 测试1: 密钥隔离验证
        print("🧪 测试1: 密钥隔离验证")
        
        # 使用不同密钥加密相同数据，结果应该不同
        test_data = "相同的测试数据用于密钥隔离验证"
        
        key1_config = KeyConfig(
            key_id="security-test-key-1",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            environment="security_test"
        )
        
        key2_config = KeyConfig(
            key_id="security-test-key-2",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            environment="security_test"
        )
        
        # 使用密钥1加密
        config1 = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key1_config,
            iv_length=12,
            tag_length=16
        )
        
        request1 = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
            data=test_data,
            encryption_config=config1,
            key_generation_config=None,
            key_export_config=None,
)
        
        response1 = await self.crypto_client.process_request(request1)
        
        # 使用密钥2加密
        config2 = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key2_config,
            iv_length=12,
            tag_length=16
        )
        
        request2 = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
            data=test_data,
            encryption_config=config2,
            key_generation_config=None,
            key_export_config=None,
)
        
        response2 = await self.crypto_client.process_request(request2)
        
        key_isolation_test = {
            "test_name": "密钥隔离验证",
            "success": False,
            "details": {}
        }
        
        if response1.success and response2.success:
            # 验证加密结果不同
            if response1.result != response2.result:
                key_isolation_test["success"] = True
                key_isolation_test["details"] = {
                    "key1_result_length": len(response1.result),
                    "key2_result_length": len(response2.result),
                    "results_different": True
                }
                print("  ✅ 密钥隔离验证通过：不同密钥产生不同加密结果")
            else:
                key_isolation_test["details"] = {
                    "results_different": False,
                    "error": "不同密钥产生了相同的加密结果"
                }
                print("  ❌ 密钥隔离验证失败：不同密钥产生了相同结果")
        else:
            key_isolation_test["details"] = {
                "key1_success": response1.success,
                "key2_success": response2.success,
                "key1_error": response1.error_message if not response1.success else None,
                "key2_error": response2.error_message if not response2.success else None
            }
            print("  ❌ 密钥隔离验证失败：加密操作失败")
        
        security_tests.append(key_isolation_test)
        
        # 测试2: 数据完整性验证
        print("\n🧪 测试2: 数据完整性验证")
        
        integrity_test = {
            "test_name": "数据完整性验证",
            "success": False,
            "details": {}
        }
        
        # 加密数据
        original_data = "数据完整性验证测试数据"
        encrypt_request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
            data=original_data,
            encryption_config=config1,
            key_generation_config=None,
            key_export_config=None,
)
        
        encrypt_response = await self.crypto_client.process_request(encrypt_request)
        
        if encrypt_response.success:
            # 解密数据
            decrypt_request = CryptoRequest(
operation=CryptoOperation.DECRYPT,
                data=encrypt_response.result,
                encryption_config=config1,
                key_generation_config=None,
                key_export_config=None,
)
            
            decrypt_response = await self.crypto_client.process_request(decrypt_request)
            
            if decrypt_response.success:
                if original_data == decrypt_response.result:
                    integrity_test["success"] = True
                    integrity_test["details"] = {
                        "original_length": len(original_data),
                        "decrypted_length": len(decrypt_response.result),
                        "data_matches": True
                    }
                    print("  ✅ 数据完整性验证通过：加密解密往返数据一致")
                else:
                    integrity_test["details"] = {
                        "original_data": original_data,
                        "decrypted_data": decrypt_response.result,
                        "data_matches": False
                    }
                    print("  ❌ 数据完整性验证失败：解密数据与原始数据不一致")
            else:
                integrity_test["details"] = {
                    "decrypt_error": decrypt_response.error_message
                }
                print(f"  ❌ 数据完整性验证失败：解密失败 - {decrypt_response.error_message}")
        else:
            integrity_test["details"] = {
                "encrypt_error": encrypt_response.error_message
            }
            print(f"  ❌ 数据完整性验证失败：加密失败 - {encrypt_response.error_message}")
        
        security_tests.append(integrity_test)
        
        # 统计安全测试结果
        passed_tests = sum(1 for test in security_tests if test["success"])
        total_tests = len(security_tests)
        
        result = {
            "test_type": "安全性验证测试",
            "mode": self.mode,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "pass_rate": (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
            "security_tests": security_tests
        }
        
        print(f"\n📊 安全性验证完成: {passed_tests}/{total_tests} 通过 ({result['pass_rate']:.1f}%)")
        return result
        
    async def run_all_performance_tests(self) -> List[Dict[str, Any]]:
        """运行所有性能测试"""
        print("🚀 开始性能测试和安全验证")
        print("=" * 60)
        print(f"运行模式: {self.mode.upper()}")
        print("=" * 60)
        
        await self.initialize()
        
        try:
            # 运行各种性能测试
            tests = [
                self.benchmark_encryption_algorithms(),
                self.benchmark_signature_algorithms(),
                self.stress_test_large_data(),
                self.security_validation_test(),
            ]
            
            results = []
            for test in tests:
                result = await test
                results.append(result)
                self.performance_results.append(result)
                
            return results
            
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("TestGenius 性能测试和安全验证演示程序")
    print("=" * 60)
    
    # 开发模式演示
    print("\n🔧 开发模式性能测试")
    dev_demo = PerformanceDemo(mode="dev")
    dev_results = await dev_demo.run_all_performance_tests()
    
    # 显示测试结果摘要
    print("\n📊 性能测试结果摘要")
    print("-" * 50)
    for i, result in enumerate(dev_results, 1):
        test_type = result.get("test_type", "未知测试")
        if "algorithms" in result:
            algo_count = len(result["algorithms"])
            print(f"{i}. {test_type}: 测试了 {algo_count} 种算法")
        elif "pass_rate" in result:
            pass_rate = result["pass_rate"]
            print(f"{i}. {test_type}: 通过率 {pass_rate:.1f}%")
        else:
            print(f"{i}. {test_type}: 已完成")
        
    print("\n🎉 性能测试和安全验证完成！")


if __name__ == "__main__":
    asyncio.run(main())
