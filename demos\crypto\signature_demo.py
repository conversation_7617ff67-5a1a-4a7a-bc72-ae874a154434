"""
TestGenius 数字签名演示模块

本模块展示各种数字签名算法的使用方法，包括：
- SM2国密数字签名算法
- RSA-PSS、RSA-PKCS1签名算法
- 不同格式数据的签名示例
- 签名生成的完整流程演示
"""

import asyncio
import time
import json
import hashlib
from pathlib import Path
from typing import Dict, Any, List
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    SignatureConfig,
    KeyConfig,
    KeyType,
    SignatureAlgorithm,
    HashAlgorithm,
)


class SignatureDemo:
    """数字签名演示类"""
    
    def __init__(self, mode: str = "dev"):
        """
        初始化数字签名演示
        
        Args:
            mode: 运行模式，'dev' 或 'production'
        """
        self.mode = mode
        self.crypto_client = None
        self.demo_results: List[Dict[str, Any]] = []
        
    async def initialize(self):
        """初始化加密客户端"""
        print(f"🔧 初始化数字签名客户端 (模式: {self.mode})")
        self.crypto_client = CryptoClient(mode=self.mode)
        await self.crypto_client.initialize()
        print("✅ 数字签名客户端初始化完成\n")
        
    async def cleanup(self):
        """清理资源"""
        if self.crypto_client:
            await self.crypto_client.cleanup()
            
    async def demo_sm2_signature(self) -> Dict[str, Any]:
        """演示SM2国密数字签名"""
        print("✍️ SM2 国密数字签名演示")
        print("-" * 50)
        
        # 测试数据
        test_data = "这是需要进行SM2数字签名的重要数据，确保数据完整性和身份认证。"
        print(f"待签名数据: {test_data}")
        
        # 配置私钥
        key_config = KeyConfig(
            key_id="demo-sm2-private-key-001",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.SM2,
            key_size=256,  # SM2使用256位密钥
            environment="demo"
        )
        
        # 配置签名参数
        signature_config = SignatureConfig(
            algorithm=SignatureAlgorithm.SM2,
            hash_algorithm=HashAlgorithm.SM3,  # SM2通常配合SM3哈希算法
            key_config=key_config
        )
        
        # 创建签名请求
        request = CryptoRequest(
operation=CryptoOperation.SIGN,
            data=test_data,
            signature_config=signature_config,
            encoding="utf-8",
            output_format="base64",
            key_generation_config=None,
            key_export_config=None,
)
        
        # 执行签名
        start_time = time.time()
        response = await self.crypto_client.process_request(request)
        end_time = time.time()
        
        result = {
            "algorithm": "SM2",
            "hash_algorithm": "SM3",
            "success": response.success,
            "original_data": test_data,
            "signature": response.result if response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000,
            "error": response.error_message if not response.success else None,
            "mode_info": f"运行模式: {self.mode}"
        }
        
        if response.success:
            print(f"✅ SM2签名成功")
            print(f"签名结果: {response.result}")
            print(f"处理时间: {result['processing_time_ms']:.2f} ms")
            print(f"签名长度: {len(response.result)} 字符")
        else:
            print(f"❌ SM2签名失败: {response.error_message}")
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM2可能使用模拟签名")
                
        print()
        return result
        
    async def demo_rsa_pss_signature(self) -> Dict[str, Any]:
        """演示RSA-PSS数字签名"""
        print("✍️ RSA-PSS 数字签名演示")
        print("-" * 50)
        
        # 测试数据
        test_data = "RSA-PSS数字签名测试数据 - TestGenius项目安全模块演示"
        print(f"待签名数据: {test_data}")
        
        # 配置私钥
        key_config = KeyConfig(
            key_id="demo-rsa-pss-private-key-001",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.RSA_PSS,
            key_size=2048,  # RSA 2048位密钥
            environment="demo"
        )
        
        # 配置签名参数
        signature_config = SignatureConfig(
            algorithm=SignatureAlgorithm.RSA_PSS,
            hash_algorithm=HashAlgorithm.SHA256,  # RSA-PSS配合SHA256
            key_config=key_config,
            salt_length=32  # PSS模式盐长度
        )
        
        # 创建签名请求
        request = CryptoRequest(
operation=CryptoOperation.SIGN,
            data=test_data,
            signature_config=signature_config,
            encoding="utf-8",
            output_format="base64",
            key_generation_config=None,
            key_export_config=None,
)
        
        # 执行签名
        start_time = time.time()
        response = await self.crypto_client.process_request(request)
        end_time = time.time()
        
        result = {
            "algorithm": "RSA-PSS",
            "hash_algorithm": "SHA256",
            "success": response.success,
            "original_data": test_data,
            "signature": response.result if response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000,
            "error": response.error_message if not response.success else None
        }
        
        if response.success:
            print(f"✅ RSA-PSS签名成功")
            print(f"签名结果: {response.result[:50]}...")
            print(f"处理时间: {result['processing_time_ms']:.2f} ms")
            print(f"签名长度: {len(response.result)} 字符")
        else:
            print(f"❌ RSA-PSS签名失败: {response.error_message}")
                
        print()
        return result
        
    async def demo_json_data_signature(self) -> Dict[str, Any]:
        """演示JSON数据签名"""
        print("📄 JSON数据数字签名演示")
        print("-" * 50)
        
        # 创建JSON测试数据
        json_data = {
            "user_id": "user123456",
            "transaction_id": "tx789012345",
            "amount": 1000.50,
            "currency": "CNY",
            "timestamp": "2024-01-15T10:30:00Z",
            "description": "TestGenius项目交易数据签名演示",
            "metadata": {
                "source": "demo",
                "version": "1.0"
            }
        }
        
        # 将JSON数据序列化为字符串（确保顺序一致）
        json_string = json.dumps(json_data, sort_keys=True, ensure_ascii=False, separators=(',', ':'))
        print(f"待签名JSON数据: {json_string}")
        
        # 配置私钥
        key_config = KeyConfig(
            key_id="demo-json-sign-key-001",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.RSA_PSS,
            key_size=2048,
            environment="demo"
        )
        
        # 配置签名参数
        signature_config = SignatureConfig(
            algorithm=SignatureAlgorithm.RSA_PSS,
            hash_algorithm=HashAlgorithm.SHA256,
            key_config=key_config,
            salt_length=32
        )
        
        # 创建签名请求
        request = CryptoRequest(
operation=CryptoOperation.SIGN,
            data=json_string,
            signature_config=signature_config,
            encoding="utf-8",
            output_format="base64",
            metadata={"data_type": "json", "original_data": json_data},
            key_generation_config=None,
            key_export_config=None,
)
        
        # 执行签名
        start_time = time.time()
        response = await self.crypto_client.process_request(request)
        end_time = time.time()
        
        result = {
            "algorithm": "RSA-PSS (JSON数据)",
            "hash_algorithm": "SHA256",
            "success": response.success,
            "original_json": json_data,
            "serialized_data": json_string,
            "signature": response.result if response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000,
            "error": response.error_message if not response.success else None
        }
        
        if response.success:
            print(f"✅ JSON数据签名成功")
            print(f"签名结果: {response.result[:50]}...")
            print(f"处理时间: {result['processing_time_ms']:.2f} ms")
            
            # 创建带签名的完整数据结构
            signed_data = {
                "data": json_data,
                "signature": response.result,
                "signature_algorithm": "RSA-PSS",
                "hash_algorithm": "SHA256",
                "timestamp": time.time()
            }
            result["signed_data_structure"] = signed_data
            print(f"完整签名数据结构已生成")
        else:
            print(f"❌ JSON数据签名失败: {response.error_message}")
                
        print()
        return result
        
    async def demo_batch_signature(self) -> Dict[str, Any]:
        """演示批量数据签名"""
        print("📦 批量数据签名演示")
        print("-" * 50)
        
        # 创建批量测试数据
        batch_data = [
            "批量签名测试数据 #1",
            "批量签名测试数据 #2 - 包含特殊字符!@#$%",
            "批量签名测试数据 #3 - 中文内容测试",
            "Batch signature test data #4 - English content",
            "批量签名测试数据 #5 - 混合内容 Mixed Content"
        ]
        
        print(f"待签名数据条数: {len(batch_data)}")
        for i, data in enumerate(batch_data, 1):
            print(f"  {i}. {data}")
        
        # 配置私钥
        key_config = KeyConfig(
            key_id="demo-batch-sign-key-001",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.SM2,
            key_size=256,
            environment="demo"
        )
        
        # 配置签名参数
        signature_config = SignatureConfig(
            algorithm=SignatureAlgorithm.SM2,
            hash_algorithm=HashAlgorithm.SM3,
            key_config=key_config
        )
        
        signatures = []
        total_start_time = time.time()
        
        # 逐个签名
        for i, data in enumerate(batch_data, 1):
            request = CryptoRequest(
operation=CryptoOperation.SIGN,
                data=data,
                signature_config=signature_config,
                encoding="utf-8",
                output_format="base64",
                metadata={"batch_index": i, "batch_total": len(batch_data,
                key_generation_config=None,
                key_export_config=None,
)}
            )
            
            start_time = time.time()
            response = await self.crypto_client.process_request(request)
            end_time = time.time()
            
            signature_result = {
                "index": i,
                "data": data,
                "success": response.success,
                "signature": response.result if response.success else None,
                "processing_time_ms": (end_time - start_time) * 1000,
                "error": response.error_message if not response.success else None
            }
            signatures.append(signature_result)
            
            status = "✅" if response.success else "❌"
            print(f"  {status} 数据 #{i} 签名完成 ({signature_result['processing_time_ms']:.2f}ms)")
        
        total_end_time = time.time()
        total_time = (total_end_time - total_start_time) * 1000
        
        successful_count = sum(1 for sig in signatures if sig["success"])
        
        result = {
            "algorithm": "SM2 (批量签名)",
            "hash_algorithm": "SM3",
            "total_count": len(batch_data),
            "successful_count": successful_count,
            "failed_count": len(batch_data) - successful_count,
            "signatures": signatures,
            "total_processing_time_ms": total_time,
            "average_processing_time_ms": total_time / len(batch_data),
            "success_rate": (successful_count / len(batch_data)) * 100
        }
        
        print(f"\n📊 批量签名统计:")
        print(f"  总数据条数: {result['total_count']}")
        print(f"  成功签名: {result['successful_count']}")
        print(f"  失败签名: {result['failed_count']}")
        print(f"  成功率: {result['success_rate']:.1f}%")
        print(f"  总处理时间: {result['total_processing_time_ms']:.2f} ms")
        print(f"  平均处理时间: {result['average_processing_time_ms']:.2f} ms")
        
        print()
        return result
        
    async def run_all_demos(self) -> List[Dict[str, Any]]:
        """运行所有数字签名演示"""
        print("🚀 开始数字签名演示")
        print("=" * 60)
        print(f"运行模式: {self.mode.upper()}")
        print("=" * 60)
        print()
        
        await self.initialize()
        
        try:
            # 运行各种签名演示
            demos = [
                self.demo_sm2_signature(),
                self.demo_rsa_pss_signature(),
                self.demo_json_data_signature(),
                self.demo_batch_signature(),
            ]
            
            results = []
            for demo in demos:
                result = await demo
                results.append(result)
                self.demo_results.append(result)
                
            return results
            
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("TestGenius 数字签名演示程序")
    print("=" * 60)
    
    # 开发模式演示
    print("\n🔧 开发模式演示")
    dev_demo = SignatureDemo(mode="dev")
    dev_results = await dev_demo.run_all_demos()
    
    # 显示演示结果摘要
    print("\n📊 演示结果摘要")
    print("-" * 50)
    for i, result in enumerate(dev_results, 1):
        if "total_count" in result:  # 批量签名结果
            status = f"✅ {result['successful_count']}/{result['total_count']} 成功"
            time_info = f"{result['total_processing_time_ms']:.2f}ms"
        else:
            status = "✅ 成功" if result["success"] else "❌ 失败"
            time_info = f"{result['processing_time_ms']:.2f}ms"
        print(f"{i}. {result['algorithm']}: {status} ({time_info})")
        
    print("\n🎉 数字签名演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
