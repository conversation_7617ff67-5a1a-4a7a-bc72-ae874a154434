"""
TestGenius 验签演示模块

本模块展示各种数字签名验证的使用方法，包括：
- 验证数字签名的有效性
- 展示签名验证的完整流程
- 包含签名验证成功和失败的场景
- 完整的签名-验签往返验证
"""

import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Tuple
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    SignatureConfig,
    KeyConfig,
    KeyType,
    SignatureAlgorithm,
    HashAlgorithm,
)


class VerificationDemo:
    """验签演示类"""
    
    def __init__(self, mode: str = "dev"):
        """
        初始化验签演示
        
        Args:
            mode: 运行模式，'dev' 或 'production'
        """
        self.mode = mode
        self.crypto_client = None
        self.demo_results: List[Dict[str, Any]] = []
        
    async def initialize(self):
        """初始化加密客户端"""
        print(f"🔧 初始化验签客户端 (模式: {self.mode})")
        self.crypto_client = CryptoClient(mode=self.mode)
        await self.crypto_client.initialize()
        print("✅ 验签客户端初始化完成\n")
        
    async def cleanup(self):
        """清理资源"""
        if self.crypto_client:
            await self.crypto_client.cleanup()
            
    async def demo_sm2_sign_verify_round_trip(self) -> Dict[str, Any]:
        """演示SM2签名验签往返"""
        print("🔄 SM2 签名验签往返演示")
        print("-" * 50)
        
        # 原始测试数据
        original_data = "这是SM2签名验签往返测试的重要数据，需要确保数据完整性和身份认证。"
        print(f"待签名数据: {original_data}")
        
        # 配置私钥（用于签名）
        private_key_config = KeyConfig(
            key_id="demo-sm2-private-key-roundtrip-001",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.SM2,
            key_size=256,
            environment="demo"
        )
        
        # 配置公钥（用于验签）
        public_key_config = KeyConfig(
            key_id="demo-sm2-public-key-roundtrip-001",
            key_type=KeyType.ASYMMETRIC_PUBLIC,
            algorithm=SignatureAlgorithm.SM2,
            key_size=256,
            environment="demo"
        )
        
        # 配置签名参数
        sign_config = SignatureConfig(
            algorithm=SignatureAlgorithm.SM2,
            hash_algorithm=HashAlgorithm.SM3,
            key_config=private_key_config
        )
        
        # 配置验签参数
        verify_config = SignatureConfig(
            algorithm=SignatureAlgorithm.SM2,
            hash_algorithm=HashAlgorithm.SM3,
            key_config=public_key_config
        )
        
        # 第一步：签名
        print("✍️ 步骤1: SM2签名")
        sign_request = CryptoRequest(
operation=CryptoOperation.SIGN,
            data=original_data,
            signature_config=sign_config,
            encoding="utf-8",
            output_format="base64",
            key_generation_config=None,
            key_export_config=None,
)
        
        sign_start_time = time.time()
        sign_response = await self.crypto_client.process_request(sign_request)
        sign_end_time = time.time()
        
        if not sign_response.success:
            result = {
                "algorithm": "SM2 (签名验签往返)",
                "success": False,
                "error": f"SM2签名失败: {sign_response.error_message}",
                "sign_time_ms": (sign_end_time - sign_start_time) * 1000,
                "mode_info": f"运行模式: {self.mode}"
            }
            print(f"❌ SM2签名失败: {sign_response.error_message}")
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM2可能使用模拟签名")
            return result
            
        signature = sign_response.result
        print(f"✅ SM2签名成功: {signature}")
        print(f"签名时间: {(sign_end_time - sign_start_time) * 1000:.2f} ms")
        
        # 第二步：验签
        print("\n🔍 步骤2: SM2验签")
        verify_request = CryptoRequest(
operation=CryptoOperation.VERIFY,
            data=original_data,
            signature_config=verify_config,
            encoding="utf-8",
            metadata={"signature": signature},
            key_generation_config=None,
            key_export_config=None,
)
        
        verify_start_time = time.time()
        verify_response = await self.crypto_client.process_request(verify_request)
        verify_end_time = time.time()
        
        result = {
            "algorithm": "SM2 (签名验签往返)",
            "success": verify_response.success,
            "original_data": original_data,
            "signature": signature,
            "verification_result": verify_response.result if verify_response.success else None,
            "signature_valid": verify_response.result if verify_response.success else False,
            "sign_time_ms": (sign_end_time - sign_start_time) * 1000,
            "verify_time_ms": (verify_end_time - verify_start_time) * 1000,
            "total_time_ms": (sign_end_time - sign_start_time + verify_end_time - verify_start_time) * 1000,
            "error": verify_response.error_message if not verify_response.success else None,
            "mode_info": f"运行模式: {self.mode}"
        }
        
        if verify_response.success:
            is_valid = verify_response.result
            print(f"✅ SM2验签完成: {'签名有效' if is_valid else '签名无效'}")
            print(f"验签时间: {result['verify_time_ms']:.2f} ms")
            result["signature_valid"] = is_valid
        else:
            print(f"❌ SM2验签失败: {verify_response.error_message}")
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM2可能使用模拟验签")
            
        print(f"总处理时间: {result['total_time_ms']:.2f} ms")
        print()
        return result
        
    async def demo_rsa_pss_sign_verify_round_trip(self) -> Dict[str, Any]:
        """演示RSA-PSS签名验签往返"""
        print("🔄 RSA-PSS 签名验签往返演示")
        print("-" * 50)
        
        # 原始测试数据
        original_data = "RSA-PSS签名验签往返测试数据 - TestGenius项目安全验证演示"
        print(f"待签名数据: {original_data}")
        
        # 配置私钥（用于签名）
        private_key_config = KeyConfig(
            key_id="demo-rsa-pss-private-key-roundtrip-001",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.RSA_PSS,
            key_size=2048,
            environment="demo"
        )
        
        # 配置公钥（用于验签）
        public_key_config = KeyConfig(
            key_id="demo-rsa-pss-public-key-roundtrip-001",
            key_type=KeyType.ASYMMETRIC_PUBLIC,
            algorithm=SignatureAlgorithm.RSA_PSS,
            key_size=2048,
            environment="demo"
        )
        
        # 配置签名参数
        sign_config = SignatureConfig(
            algorithm=SignatureAlgorithm.RSA_PSS,
            hash_algorithm=HashAlgorithm.SHA256,
            key_config=private_key_config,
            salt_length=32
        )
        
        # 配置验签参数
        verify_config = SignatureConfig(
            algorithm=SignatureAlgorithm.RSA_PSS,
            hash_algorithm=HashAlgorithm.SHA256,
            key_config=public_key_config,
            salt_length=32
        )
        
        # 第一步：签名
        print("✍️ 步骤1: RSA-PSS签名")
        sign_request = CryptoRequest(
operation=CryptoOperation.SIGN,
            data=original_data,
            signature_config=sign_config,
            encoding="utf-8",
            output_format="base64",
            key_generation_config=None,
            key_export_config=None,
)
        
        sign_start_time = time.time()
        sign_response = await self.crypto_client.process_request(sign_request)
        sign_end_time = time.time()
        
        if not sign_response.success:
            result = {
                "algorithm": "RSA-PSS (签名验签往返)",
                "success": False,
                "error": f"RSA-PSS签名失败: {sign_response.error_message}",
                "sign_time_ms": (sign_end_time - sign_start_time) * 1000
            }
            print(f"❌ RSA-PSS签名失败: {sign_response.error_message}")
            return result
            
        signature = sign_response.result
        print(f"✅ RSA-PSS签名成功: {signature[:50]}...")
        print(f"签名时间: {(sign_end_time - sign_start_time) * 1000:.2f} ms")
        
        # 第二步：验签
        print("\n🔍 步骤2: RSA-PSS验签")
        verify_request = CryptoRequest(
operation=CryptoOperation.VERIFY,
            data=original_data,
            signature_config=verify_config,
            encoding="utf-8",
            metadata={"signature": signature},
            key_generation_config=None,
            key_export_config=None,
)
        
        verify_start_time = time.time()
        verify_response = await self.crypto_client.process_request(verify_request)
        verify_end_time = time.time()
        
        result = {
            "algorithm": "RSA-PSS (签名验签往返)",
            "success": verify_response.success,
            "original_data": original_data,
            "signature": signature,
            "verification_result": verify_response.result if verify_response.success else None,
            "signature_valid": verify_response.result if verify_response.success else False,
            "sign_time_ms": (sign_end_time - sign_start_time) * 1000,
            "verify_time_ms": (verify_end_time - verify_start_time) * 1000,
            "total_time_ms": (sign_end_time - sign_start_time + verify_end_time - verify_start_time) * 1000,
            "error": verify_response.error_message if not verify_response.success else None
        }
        
        if verify_response.success:
            is_valid = verify_response.result
            print(f"✅ RSA-PSS验签完成: {'签名有效' if is_valid else '签名无效'}")
            print(f"验签时间: {result['verify_time_ms']:.2f} ms")
            result["signature_valid"] = is_valid
        else:
            print(f"❌ RSA-PSS验签失败: {verify_response.error_message}")
            
        print(f"总处理时间: {result['total_time_ms']:.2f} ms")
        print()
        return result
        
    async def demo_verification_failure_scenarios(self) -> Dict[str, Any]:
        """演示验签失败场景"""
        print("⚠️ 验签失败场景演示")
        print("-" * 50)
        
        # 配置公钥
        public_key_config = KeyConfig(
            key_id="demo-verify-failure-public-key-001",
            key_type=KeyType.ASYMMETRIC_PUBLIC,
            algorithm=SignatureAlgorithm.RSA_PSS,
            key_size=2048,
            environment="demo"
        )
        
        # 配置验签参数
        verify_config = SignatureConfig(
            algorithm=SignatureAlgorithm.RSA_PSS,
            hash_algorithm=HashAlgorithm.SHA256,
            key_config=public_key_config,
            salt_length=32
        )
        
        failure_cases = []
        
        # 失败案例1: 无效签名格式
        print("🧪 测试案例1: 无效签名格式")
        original_data = "测试数据用于无效签名格式验证"
        invalid_signature = "这不是有效的签名格式!@#$%"
        
        verify_request = CryptoRequest(
operation=CryptoOperation.VERIFY,
            data=original_data,
            signature_config=verify_config,
            encoding="utf-8",
            metadata={"signature": invalid_signature},
            key_generation_config=None,
            key_export_config=None,
)
        
        start_time = time.time()
        response = await self.crypto_client.process_request(verify_request)
        end_time = time.time()
        
        case1_result = {
            "case": "无效签名格式",
            "data": original_data,
            "signature": invalid_signature,
            "success": response.success,
            "verification_result": response.result if response.success else None,
            "error_message": response.error_message if not response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        failure_cases.append(case1_result)
        
        if response.success:
            is_valid = response.result
            status1 = f"✅ 正确识别无效签名" if not is_valid else "❌ 错误地认为签名有效"
            print(f"{status1}: 签名验证结果 = {is_valid}")
        else:
            print(f"✅ 正确处理错误: {response.error_message}")
        
        # 失败案例2: 数据被篡改
        print("\n🧪 测试案例2: 数据被篡改")
        original_data = "原始数据用于篡改测试"
        tampered_data = "被篡改的数据用于篡改测试"  # 数据被修改
        valid_signature = "dGVzdCBzaWduYXR1cmUgZm9yIHRhbXBlcmVkIGRhdGE="  # 假设这是原始数据的有效签名
        
        verify_request = CryptoRequest(
operation=CryptoOperation.VERIFY,
            data=tampered_data,  # 使用被篡改的数据
            signature_config=verify_config,
            encoding="utf-8",
            metadata={"signature": valid_signature},
            key_generation_config=None,
            key_export_config=None,
)
        
        start_time = time.time()
        response = await self.crypto_client.process_request(verify_request)
        end_time = time.time()
        
        case2_result = {
            "case": "数据被篡改",
            "original_data": original_data,
            "tampered_data": tampered_data,
            "signature": valid_signature,
            "success": response.success,
            "verification_result": response.result if response.success else None,
            "error_message": response.error_message if not response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        failure_cases.append(case2_result)
        
        if response.success:
            is_valid = response.result
            status2 = f"✅ 正确检测到篡改" if not is_valid else "❌ 未能检测到篡改"
            print(f"{status2}: 签名验证结果 = {is_valid}")
        else:
            print(f"⚠️ 处理过程出错: {response.error_message}")
        
        # 失败案例3: 空签名
        print("\n🧪 测试案例3: 空签名")
        test_data = "测试数据用于空签名验证"
        empty_signature = ""
        
        verify_request = CryptoRequest(
operation=CryptoOperation.VERIFY,
            data=test_data,
            signature_config=verify_config,
            encoding="utf-8",
            metadata={"signature": empty_signature},
            key_generation_config=None,
            key_export_config=None,
)
        
        start_time = time.time()
        response = await self.crypto_client.process_request(verify_request)
        end_time = time.time()
        
        case3_result = {
            "case": "空签名",
            "data": test_data,
            "signature": empty_signature,
            "success": response.success,
            "verification_result": response.result if response.success else None,
            "error_message": response.error_message if not response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000
        }
        failure_cases.append(case3_result)
        
        if response.success:
            is_valid = response.result
            status3 = f"✅ 正确处理空签名" if not is_valid else "❌ 错误地认为空签名有效"
            print(f"{status3}: 签名验证结果 = {is_valid}")
        else:
            print(f"✅ 正确处理错误: {response.error_message}")
        
        # 统计结果
        total_cases = len(failure_cases)
        correctly_handled = 0
        
        for case in failure_cases:
            if case["success"]:
                # 如果验签成功，检查结果是否正确（应该是False）
                if not case["verification_result"]:
                    correctly_handled += 1
            else:
                # 如果验签过程失败，也算正确处理
                correctly_handled += 1
        
        result = {
            "algorithm": "验签失败场景测试",
            "total_test_cases": total_cases,
            "correctly_handled_cases": correctly_handled,
            "incorrectly_handled_cases": total_cases - correctly_handled,
            "error_detection_rate": (correctly_handled / total_cases) * 100,
            "test_cases": failure_cases,
            "overall_success": correctly_handled == total_cases
        }
        
        print(f"\n📊 验签失败场景测试统计:")
        print(f"  总测试案例: {result['total_test_cases']}")
        print(f"  正确处理案例: {result['correctly_handled_cases']}")
        print(f"  错误检测率: {result['error_detection_rate']:.1f}%")
        
        print()
        return result
        
    async def run_all_demos(self) -> List[Dict[str, Any]]:
        """运行所有验签演示"""
        print("🚀 开始验签演示")
        print("=" * 60)
        print(f"运行模式: {self.mode.upper()}")
        print("=" * 60)
        print()
        
        await self.initialize()
        
        try:
            # 运行各种验签演示
            demos = [
                self.demo_sm2_sign_verify_round_trip(),
                self.demo_rsa_pss_sign_verify_round_trip(),
                self.demo_verification_failure_scenarios(),
            ]
            
            results = []
            for demo in demos:
                result = await demo
                results.append(result)
                self.demo_results.append(result)
                
            return results
            
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("TestGenius 验签演示程序")
    print("=" * 60)
    
    # 开发模式演示
    print("\n🔧 开发模式演示")
    dev_demo = VerificationDemo(mode="dev")
    dev_results = await dev_demo.run_all_demos()
    
    # 显示演示结果摘要
    print("\n📊 演示结果摘要")
    print("-" * 50)
    for i, result in enumerate(dev_results, 1):
        if "total_test_cases" in result:  # 失败场景测试结果
            status = f"✅ {result['correctly_handled_cases']}/{result['total_test_cases']} 正确处理"
            time_info = "失败场景测试"
        elif "signature_valid" in result:  # 往返测试结果
            signature_status = "✅ 有效" if result.get("signature_valid") else "❌ 无效"
            status = f"✅ 成功 (签名{signature_status})" if result["success"] else "❌ 失败"
            time_info = f"{result.get('total_time_ms', 0):.2f}ms"
        else:
            status = "✅ 成功" if result["success"] else "❌ 失败"
            time_info = f"{result.get('processing_time_ms', 0):.2f}ms"
        print(f"{i}. {result['algorithm']}: {status} ({time_info})")
        
    print("\n🎉 验签演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
