"""
TestGenius 加密演示模块

本模块展示各种加密算法的使用方法，包括：
- SM2、SM3、SM4等国密算法
- AES-GCM、AES-CBC等对称加密算法
- 文本和文件加密示例
- 开发模式和生产模式的区别
"""

import asyncio
import time
import os
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Tuple
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    EncryptionConfig,
    KeyConfig,
    KeyType,
    EncryptionAlgorithm,
    HashAlgorithm,
)


class EncryptionDemo:
    """加密演示类"""
    
    def __init__(self, mode: str = "dev"):
        """
        初始化加密演示
        
        Args:
            mode: 运行模式，'dev' 或 'production'
        """
        self.mode = mode
        self.crypto_client = None
        self.demo_results: List[Dict[str, Any]] = []
        
    async def initialize(self):
        """初始化加密客户端"""
        print(f"🔧 初始化加密客户端 (模式: {self.mode})")
        self.crypto_client = CryptoClient(mode=self.mode)
        await self.crypto_client.initialize()
        print("✅ 加密客户端初始化完成\n")
        
    async def cleanup(self):
        """清理资源"""
        if self.crypto_client:
            await self.crypto_client.cleanup()
            
    async def demo_aes_gcm_encryption(self) -> Dict[str, Any]:
        """演示AES-GCM加密"""
        print("🔐 AES-GCM 加密演示")
        print("-" * 50)
        
        # 测试数据
        test_data = "这是一段需要加密的敏感数据，包含中文字符！"
        print(f"原始数据: {test_data}")
        
        # 配置密钥
        key_config = KeyConfig(
            key_id="demo-aes-gcm-key-001",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            environment="demo"
        )
        
        # 配置加密参数
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config,
            iv_length=12,  # GCM模式推荐12字节IV
            tag_length=16  # GCM模式认证标签长度
        )
        
        # 创建加密请求
        request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
            data=test_data,
            encryption_config=encryption_config,
            encoding="utf-8",
            output_format="base64",
            key_generation_config=None,
            key_export_config=None,
)
        
        # 执行加密
        start_time = time.time()
        response = await self.crypto_client.process_request(request)
        end_time = time.time()
        
        result = {
            "algorithm": "AES-GCM",
            "success": response.success,
            "original_data": test_data,
            "encrypted_data": response.result if response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000,
            "error": response.error_message if not response.success else None
        }
        
        if response.success:
            print(f"✅ 加密成功")
            print(f"加密结果: {response.result[:50]}...")
            print(f"处理时间: {result['processing_time_ms']:.2f} ms")
        else:
            print(f"❌ 加密失败: {response.error_message}")
            
        print()
        return result
        
    async def demo_sm4_encryption(self) -> Dict[str, Any]:
        """演示SM4国密加密"""
        print("🔐 SM4 国密加密演示")
        print("-" * 50)
        
        # 测试数据
        test_data = "SM4国密算法加密测试数据 - TestGenius项目演示"
        print(f"原始数据: {test_data}")
        
        # 配置密钥
        key_config = KeyConfig(
            key_id="demo-sm4-key-001",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.SM4,
            key_size=128,  # SM4固定128位密钥
            environment="demo"
        )
        
        # 配置加密参数
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.SM4,
            key_config=key_config,
            mode="CBC",  # SM4 CBC模式
            iv_length=16  # SM4块大小为16字节
        )
        
        # 创建加密请求
        request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
            data=test_data,
            encryption_config=encryption_config,
            encoding="utf-8",
            output_format="base64",
            key_generation_config=None,
            key_export_config=None,
)
        
        # 执行加密
        start_time = time.time()
        response = await self.crypto_client.process_request(request)
        end_time = time.time()
        
        result = {
            "algorithm": "SM4",
            "success": response.success,
            "original_data": test_data,
            "encrypted_data": response.result if response.success else None,
            "processing_time_ms": (end_time - start_time) * 1000,
            "error": response.error_message if not response.success else None,
            "mode_info": f"运行模式: {self.mode}"
        }
        
        if response.success:
            print(f"✅ SM4加密成功")
            print(f"加密结果: {response.result[:50]}...")
            print(f"处理时间: {result['processing_time_ms']:.2f} ms")
        else:
            print(f"❌ SM4加密失败: {response.error_message}")
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM4可能使用模拟实现")
                
        print()
        return result
        
    async def demo_file_encryption(self) -> Dict[str, Any]:
        """演示文件加密"""
        print("📁 文件加密演示")
        print("-" * 50)
        
        # 创建临时测试文件
        test_content = """这是一个测试文件的内容
包含多行文本数据
用于演示文件加密功能
TestGenius 项目 - 加密模块演示
包含中文字符和特殊符号：!@#$%^&*()"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file_path = f.name
            
        try:
            print(f"测试文件路径: {temp_file_path}")
            print(f"文件大小: {os.path.getsize(temp_file_path)} 字节")
            
            # 读取文件内容
            with open(temp_file_path, 'r', encoding='utf-8') as f:
                file_data = f.read()
                
            # 配置密钥
            key_config = KeyConfig(
                key_id="demo-file-aes-key-001",
                key_type=KeyType.SYMMETRIC,
                algorithm=EncryptionAlgorithm.AES_GCM,
                key_size=256,
                environment="demo"
            )
            
            # 配置加密参数
            encryption_config = EncryptionConfig(
                algorithm=EncryptionAlgorithm.AES_GCM,
                key_config=key_config,
                iv_length=12,
                tag_length=16
            )
            
            # 创建加密请求
            request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
                data=file_data,
                encryption_config=encryption_config,
                encoding="utf-8",
                output_format="base64",
                metadata={"source_type": "file", "file_path": temp_file_path},
                key_generation_config=None,
                key_export_config=None,
)
            
            # 执行加密
            start_time = time.time()
            response = await self.crypto_client.process_request(request)
            end_time = time.time()
            
            result = {
                "algorithm": "AES-GCM (文件)",
                "success": response.success,
                "file_path": temp_file_path,
                "file_size_bytes": os.path.getsize(temp_file_path),
                "original_content_preview": file_data[:100] + "..." if len(file_data) > 100 else file_data,
                "encrypted_data": response.result if response.success else None,
                "processing_time_ms": (end_time - start_time) * 1000,
                "error": response.error_message if not response.success else None
            }
            
            if response.success:
                print(f"✅ 文件加密成功")
                print(f"加密数据长度: {len(response.result)} 字符")
                print(f"处理时间: {result['processing_time_ms']:.2f} ms")
                
                # 保存加密结果到文件
                encrypted_file_path = temp_file_path + ".encrypted"
                with open(encrypted_file_path, 'w', encoding='utf-8') as f:
                    f.write(response.result)
                print(f"加密结果已保存到: {encrypted_file_path}")
                result["encrypted_file_path"] = encrypted_file_path
            else:
                print(f"❌ 文件加密失败: {response.error_message}")
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
        print()
        return result
        
    async def run_all_demos(self) -> List[Dict[str, Any]]:
        """运行所有加密演示"""
        print("🚀 开始加密算法演示")
        print("=" * 60)
        print(f"运行模式: {self.mode.upper()}")
        print("=" * 60)
        print()
        
        await self.initialize()
        
        try:
            # 运行各种加密演示
            demos = [
                self.demo_aes_gcm_encryption(),
                self.demo_sm4_encryption(),
                self.demo_file_encryption(),
            ]
            
            results = []
            for demo in demos:
                result = await demo
                results.append(result)
                self.demo_results.append(result)
                
            return results
            
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("TestGenius 加密算法演示程序")
    print("=" * 60)
    
    # 开发模式演示
    print("\n🔧 开发模式演示")
    dev_demo = EncryptionDemo(mode="dev")
    dev_results = await dev_demo.run_all_demos()
    
    # 显示演示结果摘要
    print("\n📊 演示结果摘要")
    print("-" * 50)
    for i, result in enumerate(dev_results, 1):
        status = "✅ 成功" if result["success"] else "❌ 失败"
        print(f"{i}. {result['algorithm']}: {status} ({result['processing_time_ms']:.2f}ms)")
        
    print("\n🎉 加密演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
