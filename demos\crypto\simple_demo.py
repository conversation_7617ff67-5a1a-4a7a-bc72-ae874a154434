#!/usr/bin/env python3
"""
TestGenius 加密算法简化演示程序

本程序提供了一个简化版的加密功能演示，确保在各种环境下都能正常运行。
"""

import asyncio
import time
import json
from datetime import datetime
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    EncryptionConfig,
    SignatureConfig,
    KeyConfig,
    KeyType,
    EncryptionAlgorithm,
    SignatureAlgorithm,
    HashAlgorithm,
)


class SimpleCryptoDemo:
    """简化的加密演示类"""
    
    def __init__(self, mode: str = "dev"):
        self.mode = mode
        self.crypto_client = None
        self.results = []
        
    async def initialize(self):
        """初始化客户端"""
        print(f"🔧 初始化加密客户端 (模式: {self.mode})")
        self.crypto_client = CryptoClient(mode=self.mode)
        await self.crypto_client.initialize()
        print("✅ 客户端初始化完成\n")
        
    async def cleanup(self):
        """清理资源"""
        if self.crypto_client:
            await self.crypto_client.cleanup()
            
    async def demo_aes_encryption(self):
        """演示AES加密"""
        print("🔐 AES-GCM 加密演示")
        print("-" * 40)
        
        test_data = "Hello, TestGenius! 这是加密测试数据。"
        print(f"原始数据: {test_data}")
        
        # 配置加密参数
        key_config = KeyConfig(
            key_id="demo-aes-gcm-encryption-key-001",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256,
            environment="demo"
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config,
            iv_length=12,
            tag_length=16
        )
        
        # 创建加密请求
        request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
            data=test_data,
            encryption_config=encryption_config,
            key_generation_config=None,
            key_export_config=None,
)
        
        # 执行加密
        start_time = time.time()
        response = await self.crypto_client.process_request(request)
        end_time = time.time()
        
        processing_time = (end_time - start_time) * 1000
        
        if response.success:
            print(f"✅ 加密成功")
            print(f"加密结果: {response.result[:50]}...")
            print(f"处理时间: {processing_time:.2f} ms")
            
            result = {
                "operation": "AES-GCM加密",
                "success": True,
                "processing_time_ms": processing_time,
                "data_length": len(test_data),
                "encrypted_length": len(response.result)
            }
        else:
            print(f"❌ 加密失败: {response.error_message}")
            result = {
                "operation": "AES-GCM加密",
                "success": False,
                "error": response.error_message,
                "processing_time_ms": processing_time
            }
        
        self.results.append(result)
        print()
        return result
        
    async def demo_sm2_signature(self):
        """演示SM2签名"""
        print("✍️ SM2 数字签名演示")
        print("-" * 40)
        
        test_data = "这是需要签名的重要数据"
        print(f"待签名数据: {test_data}")
        
        # 配置签名参数
        key_config = KeyConfig(
            key_id="demo-sm2-signature-private-key-001",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.SM2,
            key_size=256,
            environment="demo"
        )
        
        signature_config = SignatureConfig(
            algorithm=SignatureAlgorithm.SM2,
            hash_algorithm=HashAlgorithm.SM3,
            key_config=key_config
        )
        
        # 创建签名请求
        request = CryptoRequest(
operation=CryptoOperation.SIGN,
            data=test_data,
            signature_config=signature_config,
            key_generation_config=None,
            key_export_config=None,
)
        
        # 执行签名
        start_time = time.time()
        response = await self.crypto_client.process_request(request)
        end_time = time.time()
        
        processing_time = (end_time - start_time) * 1000
        
        if response.success:
            print(f"✅ 签名成功")
            print(f"签名结果: {response.result}")
            print(f"处理时间: {processing_time:.2f} ms")
            
            result = {
                "operation": "SM2数字签名",
                "success": True,
                "processing_time_ms": processing_time,
                "signature_length": len(response.result),
                "mode_info": f"运行模式: {self.mode}"
            }
        else:
            print(f"❌ 签名失败: {response.error_message}")
            if self.mode == "dev":
                print("💡 提示: 在开发模式下，SM2使用模拟实现")
            
            result = {
                "operation": "SM2数字签名",
                "success": False,
                "error": response.error_message,
                "processing_time_ms": processing_time,
                "mode_info": f"运行模式: {self.mode}"
            }
        
        self.results.append(result)
        print()
        return result
        
    async def demo_hash_calculation(self):
        """演示哈希计算"""
        print("🔢 哈希计算演示")
        print("-" * 40)
        
        test_data = "TestGenius哈希计算测试数据"
        print(f"原始数据: {test_data}")
        
        # 创建哈希请求
        request = CryptoRequest(
operation=CryptoOperation.HASH,
            data=test_data,
            hash_algorithm=HashAlgorithm.SHA256,
            key_generation_config=None,
            key_export_config=None,
)
        
        # 执行哈希计算
        start_time = time.time()
        response = await self.crypto_client.process_request(request)
        end_time = time.time()
        
        processing_time = (end_time - start_time) * 1000
        
        if response.success:
            print(f"✅ 哈希计算成功")
            print(f"SHA256结果: {response.result}")
            print(f"处理时间: {processing_time:.2f} ms")
            
            result = {
                "operation": "SHA256哈希",
                "success": True,
                "processing_time_ms": processing_time,
                "hash_value": response.result
            }
        else:
            print(f"❌ 哈希计算失败: {response.error_message}")
            result = {
                "operation": "SHA256哈希",
                "success": False,
                "error": response.error_message,
                "processing_time_ms": processing_time
            }
        
        self.results.append(result)
        print()
        return result
        
    def print_summary(self):
        """打印演示总结"""
        print("📊 演示总结")
        print("=" * 50)
        
        total_demos = len(self.results)
        successful_demos = sum(1 for r in self.results if r.get("success", False))
        
        print(f"总演示数量: {total_demos}")
        print(f"成功演示: {successful_demos}")
        print(f"失败演示: {total_demos - successful_demos}")
        print(f"成功率: {(successful_demos/total_demos)*100:.1f}%")
        
        print("\n详细结果:")
        for i, result in enumerate(self.results, 1):
            status = "✅" if result.get("success") else "❌"
            operation = result.get("operation", "未知操作")
            time_info = f"{result.get('processing_time_ms', 0):.2f}ms"
            print(f"  {i}. {status} {operation} ({time_info})")
        
        # 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"simple_demo_results_{timestamp}.json"
        
        summary_data = {
            "timestamp": datetime.now().isoformat(),
            "mode": self.mode,
            "total_demos": total_demos,
            "successful_demos": successful_demos,
            "success_rate": (successful_demos/total_demos)*100 if total_demos > 0 else 0,
            "results": self.results
        }
        
        try:
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
            print(f"\n📁 结果已保存到: {result_file}")
        except Exception as e:
            print(f"⚠️ 保存结果文件失败: {e}")
        
    async def run_all_demos(self):
        """运行所有演示"""
        print("🚀 TestGenius 简化加密演示程序")
        print("=" * 50)
        print(f"运行模式: {self.mode.upper()}")
        print("=" * 50)
        print()
        
        await self.initialize()
        
        try:
            # 运行各种演示
            await self.demo_aes_encryption()
            await self.demo_sm2_signature()
            await self.demo_hash_calculation()
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            
        finally:
            await self.cleanup()
            self.print_summary()


async def main():
    """主函数"""
    demo = SimpleCryptoDemo(mode="dev")
    await demo.run_all_demos()
    
    print("\n🎉 演示完成！")
    print("=" * 50)
    print("感谢使用 TestGenius 加密模块演示程序")
    print("如需了解更多功能，请查看 demos/crypto/main_demo.py")


if __name__ == "__main__":
    asyncio.run(main())
