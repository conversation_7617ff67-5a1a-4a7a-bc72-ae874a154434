#!/usr/bin/env python3
"""
测试Orchestrator服务器启动
"""

import asyncio
import sys
from src.orchestrator.main import create_app


async def test_app_creation():
    """测试应用创建"""
    try:
        print("Testing FastAPI app creation...")
        
        app = create_app()
        print(f"✓ FastAPI app created: {app.title}")
        print(f"  Version: {app.version}")
        print(f"  Routes count: {len(app.routes)}")
        
        # 列出所有路由
        print("\n  Available routes:")
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                methods = getattr(route, 'methods', set())
                print(f"    {getattr(route, "path", "/unknown")} - {methods}")
        
        return True
        
    except Exception as e:
        print(f"✗ App creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_service_initialization():
    """测试服务初始化"""
    try:
        print("\nTesting service initialization...")
        
        from src.orchestrator.service import OrchestratorService
        
        service = OrchestratorService()
        print("✓ Service instance created")
        
        # 测试初始化
        await service.initialize()
        print("✓ Service initialized")
        
        print(f"Service ready: {service.is_ready()}")
        
        # 测试会话创建
        session = await service.create_session()
        print(f"✓ Session created: {session.session_id}")
        
        # 清理
        await service.cleanup()
        print("✓ Service cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ Service initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("TestGenius Orchestrator Server Test")
    print("=" * 40)
    
    success = True
    
    # 测试应用创建
    success &= await test_app_creation()
    
    # 测试服务初始化
    success &= await test_service_initialization()
    
    if success:
        print("\n✓ All server tests passed!")
        print("\n🚀 Ready to start the server!")
        print("Run: python -m uvicorn src.orchestrator.main:create_app --factory --reload")
    else:
        print("\n✗ Some server tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 