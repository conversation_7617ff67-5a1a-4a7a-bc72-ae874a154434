"""
TestGenius 加密签名模块全面功能检查和测试验证

本测试套件专门用于验证加密签名模块的完整性和正确性，包括：
1. 功能完整性检查
2. 算法支持验证
3. 密钥管理测试
4. 错误处理验证
5. 性能和安全测试
"""

import pytest
import asyncio
import base64
import os
import time
import sys
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import MagicMock, patch
from uuid import uuid4

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoResponse,
    CryptoOperation,
    EncryptionConfig,
    SignatureConfig,
    KeyConfig,
    KeyType,
    EncryptionAlgorithm,
    SignatureAlgorithm,
    HashAlgorithm,
    VaultConfig,
)


class TestCryptoModuleCompleteness:
    """加密模块完整性测试"""
    
    @pytest.fixture
    async def crypto_client_dev(self):
        """开发模式加密客户端"""
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()
    
    @pytest.fixture
    async def crypto_client_prod(self):
        """生产模式加密客户端（模拟）"""
        vault_config = VaultConfig(
            vault_url="http://mock-vault:8200",
            vault_token="mock_token"
        )
        client = CryptoClient(vault_config=vault_config, mode="production")
        # 注意：这里不初始化，因为没有真实的Vault
        yield client
        await client.cleanup()
    
    def test_supported_algorithms_completeness(self):
        """测试支持的算法完整性"""
        # 检查加密算法枚举
        expected_encryption_algorithms = {
            "AES-GCM", "AES-CBC", "AES-ECB", "3DES", "SM4"
        }
        actual_encryption_algorithms = {alg.value for alg in EncryptionAlgorithm}
        assert expected_encryption_algorithms.issubset(actual_encryption_algorithms)
        
        # 检查签名算法枚举
        expected_signature_algorithms = {
            "RSA-PSS", "RSA-PKCS1", "ECDSA", "SM2"
        }
        actual_signature_algorithms = {alg.value for alg in SignatureAlgorithm}
        assert expected_signature_algorithms.issubset(actual_signature_algorithms)
        
        # 检查哈希算法枚举
        expected_hash_algorithms = {
            "SHA256", "SHA1", "MD5", "SM3"
        }
        actual_hash_algorithms = {alg.value for alg in HashAlgorithm}
        assert expected_hash_algorithms.issubset(actual_hash_algorithms)
    
    @pytest.mark.asyncio
    async def test_client_initialization_modes(self, crypto_client_dev):
        """测试客户端不同模式初始化"""
        # 开发模式
        assert crypto_client_dev._mode == "dev"
        assert crypto_client_dev._initialized is True
        
        # 检查统计信息初始化
        stats = await crypto_client_dev.get_stats()
        assert stats.total_operations == 0
        assert stats.successful_operations == 0
        assert stats.failed_operations == 0
    
    @pytest.mark.asyncio
    async def test_key_cache_mechanism(self, crypto_client_dev):
        """测试密钥缓存机制"""
        # 创建测试请求
        key_config = KeyConfig(
            key_id="test-cache-key-12345",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="test data for cache",
            encryption_config=encryption_config
        )
        
        # 第一次请求 - 应该生成并缓存密钥
        response1 = await crypto_client_dev.process_request(request)
        assert response1.success is True
        
        # 检查缓存中是否有密钥
        assert key_config.key_id in crypto_client_dev._key_cache
        
        # 第二次请求 - 应该使用缓存的密钥
        response2 = await crypto_client_dev.process_request(request)
        assert response2.success is True
        
        # 两次加密结果应该不同（因为使用了随机nonce），但都应该成功
        assert response1.result != response2.result


class TestEncryptionAlgorithms:
    """加密算法测试"""
    
    @pytest.fixture
    async def crypto_client(self):
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()
    
    @pytest.mark.asyncio
    async def test_aes_gcm_round_trip(self, crypto_client):
        """测试AES-GCM加密解密往返"""
        test_data = "这是一个AES-GCM测试消息 🔐"
        
        key_config = KeyConfig(
            key_id="test-aes-gcm-key-12345",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_size=256
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        # 加密
        encrypt_request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data=test_data,
            encryption_config=encryption_config
        )
        
        encrypt_response = await crypto_client.process_request(encrypt_request)
        assert encrypt_response.success is True
        assert encrypt_response.result != test_data
        
        # 解密
        decrypt_request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data=encrypt_response.result,
            encryption_config=encryption_config
        )
        
        decrypt_response = await crypto_client.process_request(decrypt_request)
        assert decrypt_response.success is True
        assert decrypt_response.result == test_data
    
    @pytest.mark.asyncio
    async def test_sm4_algorithm_handling(self, crypto_client):
        """测试SM4算法处理（开发模式下的模拟实现）"""
        test_data = "SM4算法测试数据"
        
        key_config = KeyConfig(
            key_id="test-sm4-key-123456789",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.SM4
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.SM4,
            key_config=key_config
        )
        
        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data=test_data,
            encryption_config=encryption_config
        )
        
        response = await crypto_client.process_request(request)
        
        # 在开发模式下，SM4应该有某种处理方式（模拟或实际实现）
        assert response is not None
        # 具体的断言取决于实际的SM4实现策略


class TestSignatureAlgorithms:
    """签名算法测试"""
    
    @pytest.fixture
    async def crypto_client(self):
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()
    
    @pytest.mark.asyncio
    async def test_rsa_pss_sign_verify_round_trip(self, crypto_client):
        """测试RSA-PSS签名验签往返"""
        test_data = "这是一个RSA-PSS签名测试消息"
        
        # 私钥配置（用于签名）
        private_key_config = KeyConfig(
            key_id="test-rsa-private-key-12345",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.RSA_PSS
        )
        
        signature_config = SignatureConfig(
            algorithm=SignatureAlgorithm.RSA_PSS,
            hash_algorithm=HashAlgorithm.SHA256,
            key_config=private_key_config
        )
        
        # 签名
        sign_request = CryptoRequest(
            operation=CryptoOperation.SIGN,
            data=test_data,
            signature_config=signature_config
        )
        
        sign_response = await crypto_client.process_request(sign_request)
        assert sign_response.success is True
        assert sign_response.result is not None
        
        # 验签（使用相同的密钥配置，实际应用中应该使用公钥）
        verify_request = CryptoRequest(
            operation=CryptoOperation.VERIFY,
            data=test_data,
            signature_config=signature_config,
            metadata={"signature": sign_response.result}
        )
        
        verify_response = await crypto_client.process_request(verify_request)
        assert verify_response.success is True
        assert verify_response.result is True  # 验签成功
    
    @pytest.mark.asyncio
    async def test_sm2_algorithm_handling(self, crypto_client):
        """测试SM2算法处理"""
        test_data = "SM2签名测试数据"
        
        key_config = KeyConfig(
            key_id="test-sm2-key-123456789",
            key_type=KeyType.ASYMMETRIC_PRIVATE,
            algorithm=SignatureAlgorithm.SM2
        )
        
        signature_config = SignatureConfig(
            algorithm=SignatureAlgorithm.SM2,
            hash_algorithm=HashAlgorithm.SM3,
            key_config=key_config
        )
        
        request = CryptoRequest(
            operation=CryptoOperation.SIGN,
            data=test_data,
            signature_config=signature_config
        )
        
        response = await crypto_client.process_request(request)
        
        # 在开发模式下，SM2应该有某种处理方式
        assert response is not None
        # 可能返回模拟签名或实际签名，取决于gmssl库的可用性


class TestHashAlgorithms:
    """哈希算法测试"""
    
    @pytest.fixture
    async def crypto_client(self):
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()
    
    @pytest.mark.asyncio
    async def test_all_hash_algorithms(self, crypto_client):
        """测试所有哈希算法"""
        test_data = "哈希算法测试数据"
        
        hash_algorithms = [
            HashAlgorithm.SHA256,
            HashAlgorithm.SHA1,
            HashAlgorithm.MD5,
            HashAlgorithm.SM3
        ]
        
        expected_lengths = {
            HashAlgorithm.SHA256: 64,  # 32字节 = 64个十六进制字符
            HashAlgorithm.SHA1: 40,   # 20字节 = 40个十六进制字符
            HashAlgorithm.MD5: 32,    # 16字节 = 32个十六进制字符
            HashAlgorithm.SM3: None   # SM3可能有特殊处理
        }
        
        for algorithm in hash_algorithms:
            request = CryptoRequest(
                operation=CryptoOperation.HASH,
                data=test_data,
                hash_algorithm=algorithm
            )
            
            response = await crypto_client.process_request(request)
            
            assert response.success is True
            assert response.result is not None
            
            if expected_lengths[algorithm] is not None:
                # 对于标准算法，检查输出长度
                assert len(response.result) == expected_lengths[algorithm]
            
            # 相同输入应该产生相同输出
            response2 = await crypto_client.process_request(request)
            assert response2.result == response.result


class TestErrorHandlingAndEdgeCases:
    """错误处理和边界条件测试"""

    @pytest.fixture
    async def crypto_client(self):
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()

    @pytest.mark.asyncio
    async def test_missing_configuration_errors(self, crypto_client):
        """测试缺少配置的错误处理"""
        # 缺少加密配置
        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="test data"
        )

        response = await crypto_client.process_request(request)
        assert response.success is False
        assert "Encryption config is required" in response.error_message

        # 缺少签名配置
        request = CryptoRequest(
            operation=CryptoOperation.SIGN,
            data="test data"
        )

        response = await crypto_client.process_request(request)
        assert response.success is False
        assert "Signature config is required" in response.error_message

        # 缺少哈希算法
        request = CryptoRequest(
            operation=CryptoOperation.HASH,
            data="test data"
        )

        response = await crypto_client.process_request(request)
        assert response.success is False
        assert "Hash algorithm is required" in response.error_message

    @pytest.mark.asyncio
    async def test_invalid_key_id_validation(self, crypto_client):
        """测试无效密钥ID验证"""
        # 测试短密钥ID
        short_key_config = KeyConfig(
            key_id="short",  # 太短
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )

        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=short_key_config
        )

        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="test data",
            encryption_config=encryption_config
        )

        response = await crypto_client.process_request(request)
        assert response.success is False
        assert "Key ID must be at least 15 characters" in response.error_message

    @pytest.mark.asyncio
    async def test_unsupported_operations(self, crypto_client):
        """测试不支持的操作"""
        # 由于Pydantic会在创建时验证枚举值，我们测试验签操作但没有签名数据
        key_config = KeyConfig(
            key_id="test-verify-key-12345",
            key_type=KeyType.ASYMMETRIC_PUBLIC,
            algorithm=SignatureAlgorithm.RSA_PSS
        )

        signature_config = SignatureConfig(
            algorithm=SignatureAlgorithm.RSA_PSS,
            hash_algorithm=HashAlgorithm.SHA256,
            key_config=key_config
        )

        request = CryptoRequest(
            operation=CryptoOperation.VERIFY,
            data="test data",
            signature_config=signature_config
            # 缺少metadata中的signature字段
        )

        response = await crypto_client.process_request(request)
        assert response.success is False
        assert response.error_message is not None

    @pytest.mark.asyncio
    async def test_data_corruption_handling(self, crypto_client):
        """测试数据损坏处理"""
        # 先正常加密
        key_config = KeyConfig(
            key_id="test-corruption-key-12345",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )

        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )

        encrypt_request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="test data",
            encryption_config=encryption_config
        )

        encrypt_response = await crypto_client.process_request(encrypt_request)
        assert encrypt_response.success is True

        # 损坏加密数据
        corrupted_data = encrypt_response.result[:-10] + "corrupted"

        decrypt_request = CryptoRequest(
            operation=CryptoOperation.DECRYPT,
            data=corrupted_data,
            encryption_config=encryption_config
        )

        decrypt_response = await crypto_client.process_request(decrypt_request)
        assert decrypt_response.success is False
        assert "Decryption failed" in decrypt_response.error_message

    @pytest.mark.asyncio
    async def test_large_data_handling(self, crypto_client):
        """测试大数据处理"""
        # 测试1MB数据
        large_data = "A" * (1024 * 1024)

        key_config = KeyConfig(
            key_id="test-large-data-key-12345",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )

        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )

        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data=large_data,
            encryption_config=encryption_config
        )

        start_time = time.time()
        response = await crypto_client.process_request(request)
        processing_time = time.time() - start_time

        assert response.success is True
        assert response.result is not None
        assert processing_time < 10.0  # 应该在10秒内完成

    @pytest.mark.asyncio
    async def test_empty_and_special_data(self, crypto_client):
        """测试空数据和特殊字符数据"""
        test_cases = [
            "",  # 空字符串
            " ",  # 空格
            "\n\t\r",  # 换行符和制表符
            "🔐🛡️🔑",  # Unicode表情符号
            "测试中文数据",  # 中文
            "Special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?",  # 特殊字符
        ]

        key_config = KeyConfig(
            key_id="test-special-data-key-12345",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )

        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )

        for test_data in test_cases:
            # 加密
            encrypt_request = CryptoRequest(
                operation=CryptoOperation.ENCRYPT,
                data=test_data,
                encryption_config=encryption_config
            )

            encrypt_response = await crypto_client.process_request(encrypt_request)
            assert encrypt_response.success is True

            # 解密
            decrypt_request = CryptoRequest(
                operation=CryptoOperation.DECRYPT,
                data=encrypt_response.result,
                encryption_config=encryption_config
            )

            decrypt_response = await crypto_client.process_request(decrypt_request)
            assert decrypt_response.success is True
            assert decrypt_response.result == test_data


class TestVaultIntegration:
    """Vault集成测试"""

    @pytest.fixture
    def mock_vault_client(self):
        """模拟Vault客户端"""
        with patch('hvac.Client') as mock_client_class:
            mock_client = MagicMock()
            mock_client.is_authenticated.return_value = True

            # 模拟不同类型的密钥
            def mock_read_secret(path):
                if "symmetric" in path:
                    key_data = base64.b64encode(os.urandom(32)).decode()
                elif "rsa-private" in path:
                    # 这里应该返回RSA私钥，但为了测试简化，返回标识符
                    key_data = "RSA_PRIVATE_KEY_MOCK"
                elif "rsa-public" in path:
                    key_data = "RSA_PUBLIC_KEY_MOCK"
                else:
                    key_data = base64.b64encode(os.urandom(32)).decode()

                return {
                    'data': {
                        'data': {
                            'key': key_data
                        }
                    }
                }

            mock_client.secrets.kv.v2.read_secret_version.side_effect = mock_read_secret
            mock_client_class.return_value = mock_client

            yield mock_client

    @pytest.mark.asyncio
    async def test_vault_key_retrieval(self, mock_vault_client):
        """测试Vault密钥检索"""
        vault_config = VaultConfig(
            vault_url="http://mock-vault:8200",
            vault_token="mock_token"
        )

        client = CryptoClient(vault_config=vault_config, mode="production")
        await client.initialize()

        # 测试对称密钥检索
        key_config = KeyConfig(
            key_id="test-symmetric-vault-key",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM,
            vault_path="secret/data/symmetric-key"
        )

        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )

        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="test vault integration",
            encryption_config=encryption_config
        )

        response = await client.process_request(request)

        # 验证请求处理和Vault被调用
        assert response is not None
        mock_vault_client.secrets.kv.v2.read_secret_version.assert_called()

        await client.cleanup()

    @pytest.mark.asyncio
    async def test_vault_authentication_failure(self):
        """测试Vault认证失败"""
        with patch('hvac.Client') as mock_client_class:
            mock_client = MagicMock()
            mock_client.is_authenticated.return_value = False
            mock_client_class.return_value = mock_client

            vault_config = VaultConfig(
                vault_url="http://mock-vault:8200",
                vault_token="invalid_token"
            )

            client = CryptoClient(vault_config=vault_config, mode="production")

            # 在生产模式下，Vault认证失败应该导致初始化失败
            with pytest.raises(Exception):
                await client.initialize()

            await client.cleanup()


class TestPerformanceAndSecurity:
    """性能和安全测试"""

    @pytest.fixture
    async def crypto_client(self):
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()

    @pytest.mark.asyncio
    async def test_concurrent_operations(self, crypto_client):
        """测试并发操作"""
        async def encrypt_task(data_suffix):
            key_config = KeyConfig(
                key_id=f"test-concurrent-key-{data_suffix}",
                key_type=KeyType.SYMMETRIC,
                algorithm=EncryptionAlgorithm.AES_GCM
            )

            encryption_config = EncryptionConfig(
                algorithm=EncryptionAlgorithm.AES_GCM,
                key_config=key_config
            )

            request = CryptoRequest(
                operation=CryptoOperation.ENCRYPT,
                data=f"concurrent test data {data_suffix}",
                encryption_config=encryption_config
            )

            return await crypto_client.process_request(request)

        # 并发执行10个加密任务
        tasks = [encrypt_task(i) for i in range(10)]
        responses = await asyncio.gather(*tasks)

        # 所有任务都应该成功
        for response in responses:
            assert response.success is True
            assert response.result is not None

    @pytest.mark.asyncio
    async def test_key_cache_performance(self, crypto_client):
        """测试密钥缓存性能"""
        key_config = KeyConfig(
            key_id="test-cache-performance-key-12345",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )

        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )

        request = CryptoRequest(
            operation=CryptoOperation.ENCRYPT,
            data="cache performance test",
            encryption_config=encryption_config
        )

        # 第一次请求（应该生成密钥）
        start_time = time.time()
        response1 = await crypto_client.process_request(request)
        first_request_time = time.time() - start_time

        # 第二次请求（应该使用缓存）
        start_time = time.time()
        response2 = await crypto_client.process_request(request)
        second_request_time = time.time() - start_time

        assert response1.success is True
        assert response2.success is True

        # 第二次请求应该更快（使用缓存）
        # 注意：这个断言可能在某些情况下不稳定，仅作为性能指示
        print(f"First request time: {first_request_time:.4f}s")
        print(f"Second request time: {second_request_time:.4f}s")

    @pytest.mark.asyncio
    async def test_statistics_accuracy(self, crypto_client):
        """测试统计信息准确性"""
        initial_stats = await crypto_client.get_stats()
        initial_total = initial_stats.total_operations
        initial_successful = initial_stats.successful_operations

        # 执行一系列操作
        operations = [
            (CryptoOperation.HASH, {"hash_algorithm": HashAlgorithm.SHA256}),
            (CryptoOperation.HASH, {"hash_algorithm": HashAlgorithm.MD5}),
            (CryptoOperation.ENCRYPT, {"encryption_config": EncryptionConfig(
                algorithm=EncryptionAlgorithm.AES_GCM,
                key_config=KeyConfig(
                    key_id="test-stats-key-123456789",
                    key_type=KeyType.SYMMETRIC,
                    algorithm=EncryptionAlgorithm.AES_GCM
                )
            )}),
        ]

        successful_ops = 0
        for operation, kwargs in operations:
            request = CryptoRequest(
                operation=operation,
                data="test data for stats",
                **kwargs
            )

            response = await crypto_client.process_request(request)
            if response.success:
                successful_ops += 1

        # 检查统计信息
        final_stats = await crypto_client.get_stats()

        # 验证操作计数增加
        operations_added = final_stats.total_operations - initial_total
        successful_added = final_stats.successful_operations - initial_successful

        assert operations_added >= len(operations), f"Expected at least {len(operations)} operations added, got {operations_added}"
        assert successful_added >= successful_ops, f"Expected at least {successful_ops} successful operations, got {successful_added}"

        # 检查按操作类型的统计
        assert CryptoOperation.HASH in final_stats.operations_by_type
        hash_operations = final_stats.operations_by_type[CryptoOperation.HASH]
        assert hash_operations >= 2, f"Expected at least 2 hash operations, got {hash_operations}"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "--asyncio-mode=auto"])
