{"timestamp": "2025-06-23T18:01:48.396782", "demo_type": "key_generation", "total_demos": 4, "successful_demos": 4, "results": [{"algorithm": "AES-256对称密钥", "success": true, "processing_time_ms": 0.6887912750244141, "key_id": "demo-aes-256-key-001", "key_type": "symmetric", "key_size": 256, "fingerprint": "6f:97:d8:d6:34:90:45:3d:e1:00:51:21:2c:9f:b5:38:d2:cb:32:61:ca:c5:52:6c:c7:c6:a4:9b:3b:89:29:26", "has_symmetric_key": true, "created_at": "2025-06-23 18:01:48.331485", "stored_in_vault": false, "export_success": true, "exported_key_length": 44}, {"algorithm": "RSA-2048密钥对", "success": true, "processing_time_ms": 49.10850524902344, "key_id": "demo-rsa-2048-keypair-001", "key_type": "asymmetric_private", "key_size": 2048, "fingerprint": "40:c5:9e:04:27:23:4a:cd:9a:62:1f:c9:90:9b:de:7a:9a:18:7b:be:6d:8e:43:83:7a:9b:27:e2:b7:43:94:4b", "has_public_key": true, "has_private_key": true, "public_key_length": 451, "private_key_length": 1704, "created_at": "2025-06-23 18:01:48.381465", "stored_in_vault": false, "export_success": true, "exported_public_key_length": 451}, {"algorithm": "ECDSA P-256密钥对", "success": true, "processing_time_ms": 1.7173290252685547, "key_id": "demo-ecdsa-p256-keypair-001", "key_type": "asymmetric_private", "key_size": 256, "fingerprint": "f6:6e:7f:01:04:37:56:83:5a:d1:6c:ea:dc:da:ce:0f:05:16:50:9e:7d:9c:f7:e4:b7:ad:c5:42:bb:8c:67:6c", "has_public_key": true, "has_private_key": true, "public_key_length": 178, "private_key_length": 241, "created_at": "2025-06-23 18:01:48.389574", "stored_in_vault": false}, {"algorithm": "SM2密钥对（国密）", "success": true, "processing_time_ms": 1.3613700866699219, "mode_info": "运行模式: dev", "key_id": "demo-sm2-keypair-001", "key_type": "asymmetric_private", "key_size": 256, "fingerprint": "48:05:13:28:bd:19:03:d8:55:33:6e:04:52:77:07:80:84:9b:12:35:94:ad:81:67:66:d4:64:99:6b:61:6f:f4", "has_public_key": true, "has_private_key": true, "public_key_length": 178, "private_key_length": 241, "created_at": "2025-06-23 18:01:48.393752", "stored_in_vault": false}]}