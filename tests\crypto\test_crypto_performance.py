"""
TestGenius 加密模块性能和压力测试

本测试套件专门用于验证加密模块的性能和稳定性：
1. 高并发操作测试
2. 大数据量处理测试
3. 长时间运行稳定性测试
4. 内存使用和泄漏测试
5. 密钥缓存性能测试
"""

import pytest
import asyncio
import time
import gc
import psutil
import os
import sys
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.crypto.client import CryptoClient
from src.crypto.models import (
    CryptoRequest,
    CryptoOperation,
    EncryptionConfig,
    SignatureConfig,
    KeyConfig,
    KeyType,
    EncryptionAlgorithm,
    SignatureAlgorithm,
    HashAlgorithm,
)


class TestCryptoPerformance:
    """加密模块性能测试"""
    
    @pytest.fixture
    async def crypto_client(self):
        client = CryptoClient(mode="dev")
        await client.initialize()
        yield client
        await client.cleanup()
    
    @pytest.mark.asyncio
    async def test_high_concurrency_encryption(self, crypto_client):
        """测试高并发加密操作"""
        async def encrypt_task(task_id: int):
            key_config = KeyConfig(
                key_id=f"test-concurrent-key-{task_id:06d}",
                key_type=KeyType.SYMMETRIC,
                algorithm=EncryptionAlgorithm.AES_GCM
            )
            
            encryption_config = EncryptionConfig(
                algorithm=EncryptionAlgorithm.AES_GCM,
                key_config=key_config
            )
            
            request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
                data=f"Concurrent test data {task_id}",
                encryption_config=encryption_config,
                key_generation_config=None,
                key_export_config=None,
)
            
            start_time = time.time()
            response = await crypto_client.process_request(request)
            processing_time = time.time() - start_time
            
            return {
                'task_id': task_id,
                'success': response.success,
                'processing_time': processing_time,
                'result_length': len(response.result) if response.result else 0
            }
        
        # 并发执行100个加密任务
        num_tasks = 100
        start_time = time.time()
        
        tasks = [encrypt_task(i) for i in range(num_tasks)]
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # 分析结果
        successful_tasks = [r for r in results if r['success']]
        failed_tasks = [r for r in results if not r['success']]
        
        avg_processing_time = sum(r['processing_time'] for r in successful_tasks) / len(successful_tasks) if successful_tasks else 0
        max_processing_time = max(r['processing_time'] for r in successful_tasks) if successful_tasks else 0
        min_processing_time = min(r['processing_time'] for r in successful_tasks) if successful_tasks else 0
        
        print(f"\n=== 高并发加密测试结果 ===")
        print(f"总任务数: {num_tasks}")
        print(f"成功任务数: {len(successful_tasks)}")
        print(f"失败任务数: {len(failed_tasks)}")
        print(f"总耗时: {total_time:.2f}s")
        print(f"平均处理时间: {avg_processing_time:.4f}s")
        print(f"最大处理时间: {max_processing_time:.4f}s")
        print(f"最小处理时间: {min_processing_time:.4f}s")
        print(f"吞吐量: {len(successful_tasks)/total_time:.2f} ops/s")
        
        # 断言
        assert len(successful_tasks) >= num_tasks * 0.95  # 至少95%成功率
        assert avg_processing_time < 1.0  # 平均处理时间小于1秒
        assert total_time < 30.0  # 总时间小于30秒
    
    @pytest.mark.asyncio
    async def test_large_data_encryption(self, crypto_client):
        """测试大数据量加密性能"""
        data_sizes = [
            1024,        # 1KB
            10 * 1024,   # 10KB
            100 * 1024,  # 100KB
            1024 * 1024, # 1MB
        ]
        
        key_config = KeyConfig(
            key_id="test-large-data-key-123456789",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        results = []
        
        for size in data_sizes:
            # 生成测试数据
            test_data = "A" * size
            
            request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
                data=test_data,
                encryption_config=encryption_config,
                key_generation_config=None,
                key_export_config=None,
)
            
            # 测量加密时间
            start_time = time.time()
            response = await crypto_client.process_request(request)
            encrypt_time = time.time() - start_time
            
            if response.success:
                # 测量解密时间
                decrypt_request = CryptoRequest(
operation=CryptoOperation.DECRYPT,
                    data=response.result,
                    encryption_config=encryption_config,
                    key_generation_config=None,
                    key_export_config=None,
)
                
                start_time = time.time()
                decrypt_response = await crypto_client.process_request(decrypt_request)
                decrypt_time = time.time() - start_time
                
                results.append({
                    'size': size,
                    'size_mb': size / (1024 * 1024),
                    'encrypt_time': encrypt_time,
                    'decrypt_time': decrypt_time,
                    'total_time': encrypt_time + decrypt_time,
                    'encrypt_speed_mbps': (size / (1024 * 1024)) / encrypt_time if encrypt_time > 0 else 0,
                    'decrypt_speed_mbps': (size / (1024 * 1024)) / decrypt_time if decrypt_time > 0 else 0,
                    'success': decrypt_response.success and decrypt_response.result == test_data
                })
        
        print(f"\n=== 大数据量加密测试结果 ===")
        for result in results:
            print(f"数据大小: {result['size_mb']:.2f}MB")
            print(f"  加密时间: {result['encrypt_time']:.4f}s ({result['encrypt_speed_mbps']:.2f} MB/s)")
            print(f"  解密时间: {result['decrypt_time']:.4f}s ({result['decrypt_speed_mbps']:.2f} MB/s)")
            print(f"  往返成功: {result['success']}")
            print()
        
        # 断言
        assert all(r['success'] for r in results), "所有大数据加密解密应该成功"
        assert all(r['encrypt_speed_mbps'] > 1.0 for r in results if r['size_mb'] >= 0.1), "加密速度应该大于1MB/s"
    
    @pytest.mark.asyncio
    async def test_memory_usage_stability(self, crypto_client):
        """测试内存使用稳定性"""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        key_config = KeyConfig(
            key_id="test-memory-stability-key-123456789",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        memory_samples = []
        
        # 执行1000次加密操作
        for i in range(1000):
            request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
                data=f"Memory test data {i}",
                encryption_config=encryption_config,
                key_generation_config=None,
                key_export_config=None,
)
            
            response = await crypto_client.process_request(request)
            assert response.success
            
            # 每100次采样一次内存使用
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
                
                # 强制垃圾回收
                gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = final_memory - initial_memory
        
        print(f"\n=== 内存使用稳定性测试结果 ===")
        print(f"初始内存: {initial_memory:.2f}MB")
        print(f"最终内存: {final_memory:.2f}MB")
        print(f"内存增长: {memory_increase:.2f}MB")
        print(f"内存采样: {memory_samples}")
        
        # 断言：内存增长应该在合理范围内（小于50MB）
        assert memory_increase < 50.0, f"内存增长过大: {memory_increase:.2f}MB"
    
    @pytest.mark.asyncio
    async def test_key_cache_performance(self, crypto_client):
        """测试密钥缓存性能"""
        # 使用相同的密钥ID进行多次操作
        key_config = KeyConfig(
            key_id="test-cache-performance-key-123456789",
            key_type=KeyType.SYMMETRIC,
            algorithm=EncryptionAlgorithm.AES_GCM
        )
        
        encryption_config = EncryptionConfig(
            algorithm=EncryptionAlgorithm.AES_GCM,
            key_config=key_config
        )
        
        request = CryptoRequest(
operation=CryptoOperation.ENCRYPT,
            data="Cache performance test",
            encryption_config=encryption_config,
            key_generation_config=None,
            key_export_config=None,
)
        
        # 第一次请求（应该生成并缓存密钥）
        first_times = []
        for _ in range(10):
            start_time = time.time()
            response = await crypto_client.process_request(request)
            processing_time = time.time() - start_time
            first_times.append(processing_time)
            assert response.success
        
        # 清除缓存，重新开始
        crypto_client._key_cache.clear()
        
        # 后续请求（应该使用缓存）
        cached_times = []
        for _ in range(100):
            start_time = time.time()
            response = await crypto_client.process_request(request)
            processing_time = time.time() - start_time
            cached_times.append(processing_time)
            assert response.success
        
        avg_first_time = sum(first_times) / len(first_times)
        avg_cached_time = sum(cached_times) / len(cached_times)
        
        print(f"\n=== 密钥缓存性能测试结果 ===")
        print(f"首次请求平均时间: {avg_first_time:.4f}s")
        print(f"缓存请求平均时间: {avg_cached_time:.4f}s")
        print(f"性能提升: {avg_first_time/avg_cached_time:.2f}x")
        
        # 缓存应该提供性能提升（至少1.5倍）
        assert avg_first_time > avg_cached_time * 1.5, "密钥缓存应该显著提升性能"
    
    @pytest.mark.asyncio
    async def test_mixed_operations_performance(self, crypto_client):
        """测试混合操作性能"""
        operations = []
        
        # 准备不同类型的操作
        for i in range(50):
            # 加密操作
            operations.append({
                'type': 'encrypt',
                'request': CryptoRequest(
operation=CryptoOperation.ENCRYPT,
                    data=f"Mixed test data {i}",
                    encryption_config=EncryptionConfig(
                        algorithm=EncryptionAlgorithm.AES_GCM,
                        key_config=KeyConfig(
                            key_id=f"test-mixed-key-{i:03d}",
                            key_type=KeyType.SYMMETRIC,
                            algorithm=EncryptionAlgorithm.AES_GCM,
                            key_generation_config=None,
                            key_export_config=None,
)
                    )
                )
            })
            
            # 哈希操作
            operations.append({
                'type': 'hash',
                'request': CryptoRequest(
operation=CryptoOperation.HASH,
                    data=f"Hash test data {i}",
                    hash_algorithm=HashAlgorithm.SHA256,
                    key_generation_config=None,
                    key_export_config=None,
)
            })
            
            # 签名操作
            operations.append({
                'type': 'sign',
                'request': CryptoRequest(
operation=CryptoOperation.SIGN,
                    data=f"Sign test data {i}",
                    signature_config=SignatureConfig(
                        algorithm=SignatureAlgorithm.RSA_PSS,
                        hash_algorithm=HashAlgorithm.SHA256,
                        key_config=KeyConfig(
                            key_id=f"test-sign-key-{i:03d}",
                            key_type=KeyType.ASYMMETRIC_PRIVATE,
                            algorithm=SignatureAlgorithm.RSA_PSS,
                            key_generation_config=None,
                            key_export_config=None,
)
                    )
                )
            })
        
        # 随机打乱操作顺序
        import random
        random.shuffle(operations)
        
        # 执行所有操作
        start_time = time.time()
        results = []
        
        for op in operations:
            op_start_time = time.time()
            response = await crypto_client.process_request(op['request'])
            op_time = time.time() - op_start_time
            
            results.append({
                'type': op['type'],
                'success': response.success,
                'time': op_time
            })
        
        total_time = time.time() - start_time
        
        # 分析结果
        by_type = {}
        for result in results:
            op_type = result['type']
            if op_type not in by_type:
                by_type[op_type] = {'count': 0, 'success': 0, 'total_time': 0}
            
            by_type[op_type]['count'] += 1
            if result['success']:
                by_type[op_type]['success'] += 1
            by_type[op_type]['total_time'] += result['time']
        
        print(f"\n=== 混合操作性能测试结果 ===")
        print(f"总操作数: {len(operations)}")
        print(f"总耗时: {total_time:.2f}s")
        print(f"平均吞吐量: {len(operations)/total_time:.2f} ops/s")
        
        for op_type, stats in by_type.items():
            success_rate = stats['success'] / stats['count'] * 100
            avg_time = stats['total_time'] / stats['count']
            print(f"{op_type}: {stats['success']}/{stats['count']} ({success_rate:.1f}%) 平均时间: {avg_time:.4f}s")
        
        # 断言
        total_success = sum(r['success'] for r in results)
        success_rate = total_success / len(results)
        assert success_rate >= 0.95, f"总体成功率应该大于95%，实际: {success_rate:.2%}"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "--asyncio-mode=auto"])
