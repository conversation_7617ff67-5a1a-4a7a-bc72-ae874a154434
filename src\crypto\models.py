"""
加密签名模块的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class CryptoOperation(str, Enum):
    """加密操作类型枚举"""
    ENCRYPT = "encrypt"
    DECRYPT = "decrypt"
    SIGN = "sign"
    VERIFY = "verify"
    HASH = "hash"
    GENERATE_KEY = "generate_key"
    EXPORT_KEY_PEM = "export_key_pem"


class EncryptionAlgorithm(str, Enum):
    """加密算法枚举"""
    AES_GCM = "AES-GCM"
    AES_CBC = "AES-CBC"
    AES_ECB = "AES-ECB"
    DES3 = "3DES"
    SM4 = "SM4"


class SignatureAlgorithm(str, Enum):
    """签名算法枚举"""
    RSA_PSS = "RSA-PSS"
    RSA_PKCS1 = "RSA-PKCS1"
    ECDSA = "ECDSA"
    SM2 = "SM2"


class HashAlgorithm(str, Enum):
    """哈希算法枚举"""
    SHA256 = "SHA256"
    SHA1 = "SHA1"
    MD5 = "MD5"
    SM3 = "SM3"


class KeyType(str, Enum):
    """密钥类型枚举"""
    SYMMETRIC = "symmetric"
    ASYMMETRIC_PUBLIC = "asymmetric_public"
    ASYMMETRIC_PRIVATE = "asymmetric_private"


class KeyFormat(str, Enum):
    """密钥格式枚举"""
    PEM = "pem"
    DER = "der"
    PKCS8 = "pkcs8"
    PKCS1 = "pkcs1"
    OPENSSH = "openssh"
    RAW = "raw"


class KeyConfig(BaseModel):
    """密钥配置"""
    key_id: str = Field(..., description="密钥ID")
    key_type: KeyType = Field(..., description="密钥类型")
    algorithm: str = Field(..., description="相关算法")
    key_size: Optional[int] = Field(None, description="密钥长度")
    vault_path: Optional[str] = Field(None, description="Vault路径")
    environment: str = Field(default="test", description="环境标识")
    

class KeyGenerationConfig(BaseModel):
    """密钥生成配置"""
    key_type: KeyType = Field(..., description="密钥类型")
    algorithm: Union[EncryptionAlgorithm, SignatureAlgorithm] = Field(..., description="算法类型")
    key_size: int = Field(..., description="密钥长度")
    key_id: Optional[str] = Field(None, description="生成的密钥ID")
    public_exponent: Optional[int] = Field(65537, description="RSA公钥指数")
    curve_name: Optional[str] = Field(None, description="椭圆曲线名称")
    password: Optional[str] = Field(None, description="密钥保护密码")
    store_in_vault: bool = Field(False, description="是否存储到Vault")
    vault_path: Optional[str] = Field(None, description="Vault存储路径")


class KeyExportConfig(BaseModel):
    """密钥导出配置"""
    key_id: str = Field(..., description="密钥ID")
    export_format: KeyFormat = Field(..., description="导出格式")
    include_private: bool = Field(False, description="是否包含私钥")
    password: Optional[str] = Field(None, description="密钥保护密码")
    vault_path: Optional[str] = Field(None, description="Vault路径")
    

class EncryptionConfig(BaseModel):
    """加密配置"""
    algorithm: EncryptionAlgorithm = Field(..., description="加密算法")
    key_config: KeyConfig = Field(..., description="密钥配置")
    mode: Optional[str] = Field(None, description="加密模式")
    padding: Optional[str] = Field(None, description="填充模式")
    iv_length: Optional[int] = Field(None, description="初始化向量长度")
    tag_length: Optional[int] = Field(None, description="标签长度(GCM模式)")
    

class SignatureConfig(BaseModel):
    """签名配置"""
    algorithm: SignatureAlgorithm = Field(..., description="签名算法")
    hash_algorithm: HashAlgorithm = Field(..., description="哈希算法")
    key_config: KeyConfig = Field(..., description="密钥配置")
    padding: Optional[str] = Field(None, description="填充模式")
    salt_length: Optional[int] = Field(None, description="盐长度")


class CryptoRequest(BaseModel):
    """加密请求"""
    request_id: UUID = Field(default_factory=uuid4, description="请求ID")
    operation: CryptoOperation = Field(..., description="操作类型")
    data: Union[str, bytes, Dict[str, Any]] = Field(..., description="待处理数据")
    encryption_config: Optional[EncryptionConfig] = Field(None, description="加密配置")
    signature_config: Optional[SignatureConfig] = Field(None, description="签名配置")
    key_generation_config: Optional[KeyGenerationConfig] = Field(None, description="密钥生成配置")
    key_export_config: Optional[KeyExportConfig] = Field(None, description="密钥导出配置")
    hash_algorithm: Optional[HashAlgorithm] = Field(None, description="哈希算法")
    encoding: str = Field(default="utf-8", description="编码格式")
    output_format: str = Field(default="base64", description="输出格式")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    class Config:
        json_encoders = {UUID: str}


class KeyGenerationResult(BaseModel):
    """密钥生成结果"""
    key_id: str = Field(..., description="密钥ID")
    key_type: KeyType = Field(..., description="密钥类型")
    algorithm: str = Field(..., description="算法类型")
    key_size: int = Field(..., description="密钥长度")
    public_key_pem: Optional[str] = Field(None, description="公钥PEM格式")
    private_key_pem: Optional[str] = Field(None, description="私钥PEM格式")
    fingerprint: Optional[str] = Field(None, description="密钥指纹")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    stored_in_vault: bool = Field(False, description="是否已存储到Vault")
    vault_path: Optional[str] = Field(None, description="Vault存储路径")
    
    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class CryptoResponse(BaseModel):
    """加密响应"""
    request_id: UUID = Field(..., description="请求ID")
    success: bool = Field(..., description="操作是否成功")
    operation: CryptoOperation = Field(..., description="操作类型")
    result: Optional[Union[str, bytes, bool, KeyGenerationResult, Dict[str, Any]]] = Field(None, description="操作结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    processing_time: float = Field(..., description="处理时长(毫秒)")
    algorithm_used: Optional[str] = Field(None, description="使用的算法")
    key_id_used: Optional[str] = Field(None, description="使用的密钥ID")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="响应元数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    
    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda v: v.isoformat()
        }


class VaultConfig(BaseModel):
    """Vault配置"""
    vault_url: str = Field(..., description="Vault服务地址")
    vault_token: Optional[str] = Field(None, description="Vault令牌")
    vault_role: Optional[str] = Field(None, description="Vault角色")
    vault_namespace: Optional[str] = Field(None, description="Vault命名空间")
    engine_path: str = Field(default="secret", description="引擎路径")
    ca_cert_path: Optional[str] = Field(None, description="CA证书路径")
    client_cert_path: Optional[str] = Field(None, description="客户端证书路径")
    client_key_path: Optional[str] = Field(None, description="客户端密钥路径")
    timeout: int = Field(default=30, description="超时时间(秒)")
    

class CryptoStats(BaseModel):
    """加密统计信息"""
    total_operations: int = Field(default=0, description="总操作数")
    successful_operations: int = Field(default=0, description="成功操作数")
    failed_operations: int = Field(default=0, description="失败操作数")
    average_processing_time: float = Field(default=0.0, description="平均处理时间(毫秒)")
    operations_by_type: Dict[str, int] = Field(default_factory=dict, description="按类型统计")
    operations_by_algorithm: Dict[str, int] = Field(default_factory=dict, description="按算法统计")
    last_operation_time: Optional[datetime] = Field(None, description="最后操作时间")
    
    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()} 