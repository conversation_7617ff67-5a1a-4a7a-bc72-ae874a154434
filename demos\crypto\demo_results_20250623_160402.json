{"timestamp": "2025-06-23T16:04:02.116216", "total_execution_time_seconds": 0.014564275741577148, "performance_stats": {}, "demo_results": [{"algorithm": "AES-GCM", "success": false, "original_data": "这是一段需要加密的敏感数据，包含中文字符！", "encrypted_data": null, "processing_time_ms": 0.13828277587890625, "error": "Logger._log() got an unexpected keyword argument 'request_id'", "category": "加密"}, {"algorithm": "SM4", "success": false, "original_data": "SM4国密算法加密测试数据 - TestGenius项目演示", "encrypted_data": null, "processing_time_ms": 0.07748603820800781, "error": "Logger._log() got an unexpected keyword argument 'request_id'", "mode_info": "运行模式: dev", "category": "加密"}, {"algorithm": "AES-GCM (文件)", "success": false, "file_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpyh48omjs.txt", "file_size_bytes": 179, "original_content_preview": "这是一个测试文件的内容\n包含多行文本数据\n用于演示文件加密功能\nTestGenius 项目 - 加密模块演示\n包含中文字符和特殊符号：!@#$%^&*()", "encrypted_data": null, "processing_time_ms": 0.2040863037109375, "error": "Logger._log() got an unexpected keyword argument 'request_id'", "category": "加密"}], "summary": {"total_demos": 3, "successful_demos": 0, "success_rate": 0.0}}